<?php

namespace App\Http\Controllers\ContactList;

use App\Exports\ContactExport;
use App\Http\Controllers\Controller;
use App\Jobs\ImportCsvJob;
use App\Models\Contact;
use App\Models\ContactList;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\File;
use Inertia\Inertia;

use Maatwebsite\Excel\Facades\Excel;
use PDOException;

use function PHPSTORM_META\type;

class ContactListController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new ContactList())->getTable();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        $totalCount = $_GET['perPage'] ?? 10;
        $column = $_GET['column'] ?? 'id';
        $sortBy = $_GET['sort'] ?? 'desc';

        $query = ContactList::orderBy($column, $sortBy)->accessibleByUser();
        $query->with('user:id,username');
        $data["contactLists"] = $query->paginate($totalCount)->withQueryString();
        $data['getData'] = $_GET;

        $models = [
            'contact' => Contact::class,
        ];

        foreach ($models as $key => $model) {
            $table = (new $model())->getTable();
            $data["can_" . $key]["add"] = request()->user()->can($table . ".add");
            $data["can_" . $key]["edit"] = request()->user()->can($table . ".edit");
            $data["can_" . $key]["view"] = request()->user()->can($table . ".view");
            $data["can_" . $key]["delete"] = request()->user()->can($table . ".delete");
        }

        $data["can_add"] = request()->user()->can($this->tableName . ".add");
        $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
        $data["can_delete"] = request()->user()->can($this->tableName . ".delete");

        if (request()->header('Accept') == 'application/json') {
            return response()->json($data);
        }
        return Inertia::render('ContactList/Index', ["collection" => $data]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        return Inertia::render('ContactList/Add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        $validated = $request->validate([
            'name' => 'required|max:48'
        ]);

        try {
            ContactList::create(["name" => $request->name, "user_id" => Auth::id()]);
            if (request()->header('Accept') == 'application/json') {
                return response()->json(["message" => 'Contact List Created', "status" => true]);
            }
            return redirect()->back()->with(["message" => "Contact List Created !!", "type" => "success"]);
        } catch (Exception $e) {
            if (request()->header('Accept') == 'application/json') {
                return response()->json(["message" => 'Contact List Fail to create', "status" => false]);
            }
            return redirect()->back()->with("message", errorMessage())->with("type", "failure");
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        $totalCount = $_GET['perPage'] ?? 10;
        $column = $_GET['column'] ?? 'id';
        $sortBy = $_GET['sort'] ?? 'desc';

        $query = ContactList::where('id', $id)->accessibleByUser();
        $data["contactList"] = $query->first();

        if (is_null($data["contactList"])) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => 'Contact List Not Found.', 'status' => true])
                : redirect()->back()->with(["message" => 'Contact List Not Found.', "type" => "failure"]);
        }
        $data["contacts"] = Contact::orderBy($column, $sortBy)->where("contact_list_id", $id)->paginate($totalCount)->withQueryString();
        $data['getData'] = $_GET;
        if (request()->header('Accept') == 'application/json') {
            return response()->json($data);
        }

        $data["can_add"] = request()->user()->can($this->tableName . ".add");
        $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
        $data["can_delete"] = request()->user()->can($this->tableName . ".delete");

        return Inertia::render('ContactList/View', ["collection" => $data]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
        try {
            $data = array_filter($request->all());
            ContactList::where("id", $id)->update($data);

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Contact List Updated !!", "status" => true])
                :
                redirect()->back()->with(["message" => "Contact List Updated !!", "type" => "success"]);
        } catch (PDOException $e) {
            // return $e->errorInfo;
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->errorInfo[1]), "status" => false])
                :
                redirect()->back()->with(["message" => errorMessage($e->errorInfo[1]), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }

        try {
            $ids = explode(",", $id);
            $userIds = request()->user()->getAllChildUserIds();
            $contacts = ContactList::whereIn('user_id', $userIds)->whereIn('id', $ids)->get();
            $status = true;
            $message = "Record(s) Deleted.";
            if ($contacts->count()) {
                ContactList::whereIn('id', $ids)->delete();
            } else {
                $message = "Nothing to Delete.";
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => $message, "status" => $status])
                :
                redirect()->back()->with(["message" => $message, "type" => "success"]);
        } catch (Exception $e) {
            if ($e->getCode() == 23000) {
                return redirect()->back()->with(["message" => errorMessage(5), "type" => "failure"]);
            }
            return redirect()->back()->with(["message" => errorMessage(), "type" => "failure"]);
        }
    }
    public function importCSV(Request $request)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
        $request->validate([
            'attach' => 'required|mimes:csv,txt', // Allow .txt to catch potential misnamed CSVs
        ]);

        try {
            $contactList = ContactList::find($request->contact_list_id["id"]);
            if (!$contactList) {
                return redirect()->back()->with(["message" => "ContactList not Found"]);
            }
            $file = $request->file('attach');
            // $filename = $file->getClientOriginalName();


            $header = null;
            $recordCount = 0;
            $requiredHeaders = ["name", "email", "mobile"];
            if (($handle = fopen($file, 'r')) !== false) {
                while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                    if (!$header) {
                        $header = $row;
                    } else {
                        $recordCount++;
                    }
                }
                fclose($handle);
            } else {
                return redirect()->back()->with(["message" => "Unable to open the attachment", "type" => "failure"]);
            }
            $missingHeaders = array_diff($header, $requiredHeaders);

            if (!empty($missingHeaders)) {
                return redirect()->back()->withErrors(["attach" => "Wrong Headers Found in CSV"]);
            }
            $filename = $file->getClientOriginalName();

            $newFilename = uniqid() . "-$filename";
            $path = storeFile('uploads/' . $this->tableName . '/csv', $file, $newFilename);
            if ($path) {
                (ImportCsvJob::dispatch(Auth::id(), $path, $request->contact_list_id["id"]));
                return redirect()->back()->with(["message" => "Import inProgress", "type" => "success"]);
            } else {
                return redirect()->back()->with(["message" => "File Upload Failed", "type" => "failure"]);
            }
        } catch (Exception $e) {
            return redirect()->back()->with([
                "message" => errorMessage(),
                "type" => 'failure'
            ]);
        }
    }

    public function exportCSV($id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
        try {
            return Excel::download(new ContactExport($id), time() . '-contact-' . $id . '.csv', \Maatwebsite\Excel\Excel::CSV);
        } catch (Exception) {
            return null;
        }
    }
    function removeDuplicates($id, $column = 'mobile')
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
      
        $data['status'] = true;
        $data['message'] = 'Removed SuccessFully.';
        if (!($column == 'mobile' || $column == 'email')) {
            $data['status'] = false;
            $data['message'] = 'Invalid Column.';
            return response()->json($data);
        }
        try {


            DB::table('contact as t1')
                ->join('contact as t2', function ($join) use ($column) {
                    $join->on("t1.$column", '=', "t2.$column")
                        ->on('t1.contact_list_id', '=', 't2.contact_list_id')
                        ->whereRaw('t1.id > t2.id');
                })
                ->where('t1.contact_list_id', $id)
                ->whereNotNull("t1.$column") // Exclude rows where t1.$column is NULL
                ->whereNotNull("t2.$column") // Exclude rows where t2.$column is NULL
                ->delete();


            return response()->json($data);
        } catch (Exception $e) {

            $data['status'] = false;
            $data['message'] = 'Removed fail';
            return response()->json($data);
        }
    }
}
