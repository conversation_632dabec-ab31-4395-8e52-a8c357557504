<?php

namespace App\Http\Controllers\Voice;

use App\Http\Controllers\Controller;
use App\Models\Voice\Template;
use App\Models\Voice\TemplateCategory;
use App\Models\Voice\TemplateView;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TemplateController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new Template())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        try {
            $totalCount = $_GET['perPage'] ?? 8;
            $data['category_id'] = $_GET["category_id"] ?? null;
            $userIds = request()->user()->getAllChildUserIds();

            $templates = TemplateView::query();
            $templates = $templates->whereIn("user_id", $userIds);
            if ($data['category_id'] > 0) {
                $templates = $templates->where("mail_template_category_id", $data['category_id']);
            }

            $templates = $templates->orderBy('datetime', 'desc');
            $data['templates'] = $templates->paginate($totalCount)->withQueryString();

            $data['getData'] = $_GET;

            return (request()->header('Accept') == 'application/json')
                ? response()->json(['collection' => $data])
                : Inertia::render('Voice/Templates/Index', ['collection' => $data]);
        } catch (Exception $e) {

            return (request()->header('Accept') == 'application/json')
                ? response()->json(['status' => false, 'message' => errorMessage($e->getCode())])
                : redirect()->back()->with(['type' => "failure", 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(
            [
                'templateName' => 'required|max:45',
                'category_id' => 'required',
                'message' => 'required',
                // 'message' => 'bail|required_without:files',
                // 'files' => 'array|required_without:message'
            ],
            [
                'msg.required_without' => "Please ensure that you provide either a message or a file upload, or both.",
                'files.required_without' => "Please ensure that you provide either a message or a file upload, or both."
            ],
            [
                "templateName" => 'Template Name',
                "msg" => 'Message',
                "message" => 'Message',
                "category_id" => 'Category',
            ],
        );

        try {
            $data = $request->all();
            $fileName = null;
            $template = new Template();
            $template->name = $data["templateName"];
            if ($request->hasFile('files')) {
                if ((array_key_exists('files', $data)) && count($data["files"]) > 0) {
                    foreach ($data["files"] as $key => $value) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = storeFile('uploads/' . $this->tableName, $value, $fileName);
                        $template->file = $path;
                    }
                }
            }
            $template->mail_template_category_id = $data["category_id"];
            $template->body = $data["message"];
            if ($request->hasFile('files')) {
                if ((array_key_exists('files', $data)) && count($data["files"]) > 0) {
                    foreach ($data["files"] as $key => $value) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $tableName = $template->getTable();
                        $path = storeFile('uploads/' . $tableName, $value, $fileName);
                        $template->file = $path;
                    }
                }
            }
            $template->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Template Added", "status" => true])
                :
                redirect()->back()->with(["message" => "Template Added", "type" => "success"]);
        } catch (Exception $e) {

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $userIds = request()->user()->getAllChildUserIds();
            $templates = TemplateView::query();
            $data['template'] = $templates->whereIn('user_id', $userIds)->where("id", $id)->first();
            return response()->json(['collection' => $data]);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate(
            [
                'templateName' => 'required|max:45',
                'category_id' => 'required',
                'message' => 'bail|required_without:files',
                'files' => 'array|required_without:message'
            ],
            [
                'msg.required_without' => "Please ensure that you provide either a message or a file upload, or both.",
                'files.required_without' => "Please ensure that you provide either a message or a file upload, or both."
            ],
            [
                "templateName" => 'Template Name',
                "msg" => 'Message',
                "message" => 'Message',
                "category_id" => 'Category',
            ],
        );
        $message = "Template Updated.";

        try {
            $data = $request->all();
            $fileName = null;
            $template = Template::find($id);
            if ($template) {
                $template->name = $data["templateName"];
                if ($request->hasFile('files')) {
                    if ((array_key_exists('files', $data)) && count($data["files"]) > 0) {
                        foreach ($data["files"] as $key => $value) {
                            $fileName = time() . '-' . $value->getClientOriginalName();
                            $path = storeFile('uploads/' . $this->tableName, $value, $fileName);
                            $template->file = $path;
                        }
                    }
                }
                $template->mail_template_category_id = $data["category_id"];
                $template->body = $data["message"];
                if ($request->hasFile('files')) {
                    if ((array_key_exists('files', $data)) && count($data["files"]) > 0) {
                        foreach ($data["files"] as $key => $value) {
                            $fileName = time() . '-' . $value->getClientOriginalName();
                            $tableName = $template->getTable();
                            $path = storeFile('uploads/' . $tableName, $value, $fileName);
                            $template->file = $path;
                        }
                    }
                }
                $template->update();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(["message" => $message, "status" => true])
                    :
                    redirect()->back()->with(["message" => $message, "type" => "success"]);
            } else {
                $message = "Invalid Request.";
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(["message" => $message, "status" => false])
                    :
                    redirect()->back()->with(["message" => $message, "type" => "failure"]);
            }
        } catch (Exception $e) {
            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['status' => false, 'message' => $message])
                : redirect()->back()->with(['type' => 'failure', 'message' => $message]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $ids = explode(",", $id);
            $Templates = Template::whereIn('id', $ids)->with('category')->get();
            if (count($Templates) > 0) {
                $totalDeleted = 0;
                foreach ($Templates as $template) {
                    if ($template->category->user_id == Auth::id()) {
                        $totalDeleted++;
                        if ($template->file) {
                            deleteFile($template->file);
                        }
                        $template->delete();
                    }
                }
                if ($totalDeleted > 0) {
                    return (request()->header('Accept') == 'application/json') ?
                        response()->json(['message' => "Template deleted.", 'status' => true])
                        : redirect()->back()->with(["message" => "Template deleted.", "type" => "success"]);
                }
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Template NotFound.", 'status' => false])
                : redirect()->back()->with(["message" => "Record Not Found.", "type" => "failure"]);
        } catch (Exception $e) {
            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => false])
                : redirect()->back()->with(["message" => $message, "type" => "failure"]);
        }
    }


    public function removeFile($id)
    {
        try {

            $message = 'Attachment Removed';
            $template = Template::where(['id' => $id])->first();
            if ($template) {
                if ($template->file) {
                    deleteFile($template->file);
                    $template->file = null;
                    $template->update();
                }
            } else {
                $message = 'Invalid Request.';
            }
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['status' => true, 'message' => $message])
                : redirect()->back()->with(['type' => 'success', 'message' => $message]);
        } catch (Exception $e) {
            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['status' => false, 'message' => $message])
                : redirect()->back()->with(['type' => 'failure', 'message' => $message]);
        }
    }
}
