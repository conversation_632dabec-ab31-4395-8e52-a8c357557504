<?php
namespace App\Http\Controllers\Integration\WhatsApp;


use App\Http\Controllers\Controller;
use App\Models\Settings;
use App\Models\WaBot;
use App\Models\WaChannel;
use App\Models\WaMsgs;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use PDOException;

class WhatsAppWebController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $totalCount = $_GET['perPage'] ?? 10;
        $column = $_GET['column'] ?? 'id';
        $sortBy = $_GET['sort'] ?? 'desc';
        $channels = WaChannel::orderBy($column, $sortBy)
            ->where('user_id', Auth::id())
            ->with('worker', 'user', 'bot')->paginate($totalCount)->withQueryString();
        $settingData = Settings::all();
        return Inertia::render('Integration/WhatsApp/WhatsAppWeb/Index', ['channelsData' => $channels, 'getData' => $_GET, "settingsData" => $settingData]);
    }

    public function getChat($channel)
    {
        try {

            $savedChat =
                WaMsgs::where('wa_gateway_id', $channel)->with('user')->get();
            return response()->json(['chats' => $savedChat, 'count' => $savedChat->count()]);
        } catch (PDOException $e) {
            return redirect()->back()->with(["message" => $e->errorInfo[2], "type" => "failure"]);
        }
        // return "{$channel}";
    }
    public function updateSaveChat($channelID)
    {
        try {
            $channel = WaChannel::find($channelID);
            $setSaveChat = $channel->savechat == 1 ? 0 : 1;
            $channel->savechat = $setSaveChat;
            $channel->update();
        } catch (Exception $e) {
            return false;
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Integration/WhatsApp/WhatsAppWeb/Add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(
            [
                'name' => 'required|min:3',
                'autoreply' => 'required_if:isAutoReply,"1"|nullable',
                'delaySeconds' => 'numeric|required|min:10',
            ],
            [
                'autoreply.required_if' => 'Please Select AutoReply'
            ],
            [
                'name' => 'Name',
                'autoreply' => 'Auto Replies',
                'randomDelayFrom' => 'Delay From',
                'randomDelayTo' => 'Delay To',
                'randomDelay' => 'Delay Type',
                'delaySeconds' => 'Fix Delay',
                'savechat' => 'Save Chat',
                'isAutoReply' => 'Auto Reply',
            ]
        );
        try {
            $Channel = new WaChannel();
            $Channel->name = $request->name;
            $Channel->randomDelay = $request->randomDelay;
            $Channel->randomDelayFrom = $request->randomDelayFrom;
            $Channel->randomDelayTo = $request->randomDelayTo;
            $Channel->delaySeconds = $request->delaySeconds;
            $Channel->savechat = $request->savechat;
            $Channel->webhookUrl = $request->webhookUrl;
            $Channel->autoReply = $request->isAutoReply;
            if ($request->isAutoReply == 1) {
                $Channel->wa_bot_id = $request->autoreply;
            }
            $Channel->user_id = Auth::id();
            $Channel->save();
            return redirect()->back()->with(["message" => "Channel Created.", "type" => "success"]);
        } catch (PDOException $e) {
            return redirect()->back()->with(["message" => $e->errorInfo[2], "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $backData['channel'] = WaChannel::where('id', $id)
            ->where('user_id', Auth::id())
            ->with('autoReplyData')->first();
        if ($backData['channel']) {
            $backData['autoReplies'] = WaBot::where('user_id', Auth::id())->get();
            return response()->json($backData);
        } else {
            session()->flash('message', 'Record Not Found.');
            session()->flash("type", "failure");
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update($id, Request $request)
    {
        $request->validate(
            [
                'name' => 'required|min:3',
                'wa_bot_id' => 'required_if:autoReply,1|nullable',
                'delaySeconds' => 'numeric|required|min:10'

            ],
            ['wa_bot_id.required_if' => 'Please Select AutoReply'],
            [
                'name' => 'Name',
                'wa_bot_id' => 'Auto Replies',
                'randomDelayFrom' => 'Delay From',
                'randomDelayTo' => 'Delay To',
                'randomDelay' => 'Delay Type',
                'delaySeconds' => 'Fix Delay',
                'savechat' => 'Save Chat',
                'isAutoReply' => 'Auto Reply',

            ]
        );

        $channel = WaChannel::find($id);
        if ($channel) {
            try {
                $channel->name = $request->name;
                $channel->randomDelay = $request->randomDelay;
                $channel->randomDelayFrom = $request->randomDelayFrom;
                $channel->randomDelayTo = $request->randomDelayTo;
                $channel->delaySeconds = $request->delaySeconds;
                $channel->savechat = $request->savechat;
                $channel->webhookUrl = $request->webhookUrl;
                $channel->autoReply = $request->autoReply;
                if ($request->autoReply == 1) {
                    $channel->wa_bot_id = $request->wa_bot_id;
                }
                $channel->user_id = Auth::id();
                $channel->save();
                return redirect()->route('Integration.whatsapp.whatsappweb.index')->with(["message" => "Channel Updated.", "type" => "success"]);
            } catch (PDOException $e) {
                return redirect()->back()->with(["message" => $e->errorInfo[2], "type" => "failure"]);
            }
        }
        return redirect()->back()->with(["message" => 'Resource not Found. ', "type" => "failure"]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        DB::beginTransaction();
        try {
            $WaChannel = DB::table((new WaChannel())->getTable())->where('user_id', Auth::id())->whereIn('id', explode(',', $id))->delete();
            DB::commit();
            session()->flash('message', 'Gateway Deleted.');
            session()->flash("type", "success");
            return redirect()->back();
        } catch (PDOException $e) {
            DB::rollback();
            session()->flash('message', errorMessage());
            session()->flash("type", "failure");
            return redirect()->back();
        }
    }

}
