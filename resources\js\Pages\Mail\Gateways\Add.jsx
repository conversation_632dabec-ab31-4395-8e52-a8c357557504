import { button_custom, input_custom } from '@/Pages/Helpers/DesignHelper';
import { useForm } from '@inertiajs/react';
import { Button, Label, TextInput } from 'flowbite-react';
import { useState } from 'react';

function Add({ onClose }) {

    // Define input fields
    const inputFields = [
        { id: 'name', label: 'Name:', type: 'text', placeholder: 'e.g., server 1', helperText: 'Name for future reference.' },
        { id: 'host', label: 'Host:', type: 'text', placeholder: 'e.g., smtp.gmail.com' },
        { id: 'username', label: 'Email Address:', type: 'email', placeholder: 'e.g., <EMAIL>' },
        { id: 'password', label: 'Password:', type: 'text', placeholder: 'Your Password' },
        { id: 'port', label: 'Port:', type: 'number', placeholder: 'e.g., 993' },
        { id: 'limit', label: 'Limit Per Minute:', type: 'number', placeholder: '5', min: 1, helperText: 'Set limit for send mail per minute.' },
    ];

    const [formLoader, setFormLoader] = useState(false);

    const { data, setData, post, errors } = useForm({
        name: '',
        host: '',
        username: '',
        password: '',
        port: '',
        limit: '',

    });

    // Handle input change
    const handleChange = (e) => {
        setData({ ...data, [e.target.name]: e.target.value });
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();
        setFormLoader(true);
        post(route('mail.gateways.store'), {
            onSuccess: () => {
                onClose();
                setFormLoader(false);
            },
            onError: (error) => {
                console.error(error);
                setFormLoader(false);
            }
        });
    };

    return (
        <div className="w-full">
            <form onSubmit={handleSubmit}>

                {/* Dynamic input fields */}
                {inputFields.map((field) => (
                    <div key={field.id} className="mb-3">
                        <Label htmlFor={field.id}>{field.label}</Label>
                        <TextInput
                            id={field.id}
                            theme={input_custom}
                            sizing='sm'
                            name={field.id}
                            type={field.type}
                            value={data[field.id]}
                            onChange={handleChange}
                            placeholder={field.placeholder}
                            required
                            min={field.min}
                            color={errors[field.id] && 'failure'}
                            helperText={errors[field.id] ? errors[field.id] : field.helperText}
                            className="mt-1"
                        />
                    </div>
                ))}

                {/* Submit button */}
                <div className="flex justify-end mb-4">
                    <Button theme={button_custom} isProcessing={formLoader} disabled={formLoader} size='sm' type="submit" color="blue">Submit</Button>
                </div>
            </form>
        </div>
    )
}

export default Add
