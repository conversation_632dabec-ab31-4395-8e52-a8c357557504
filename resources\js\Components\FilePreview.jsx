import { getIconForFile, isImage, removePrefix, showAsset } from "@/Pages/Helpers/Helper";
import { Avatar } from "flowbite-react";

export const FilePreview = ({ object, iconOnly = false, imageSize = "md", width = "", style = { maxWidth: '400px', minWidth: '160px' }, fileBg = "bg-gray-100" }) => {
    if (!object) return null;

    const fileName = removePrefix(object instanceof File ? object.name : object);
    const isImageFile = isImage(fileName);
    const imgSrc = isImageFile && (object instanceof File ? URL.createObjectURL(object) : showAsset(object));

    return isImageFile ? (
        <div className={`flex items-center justify-center m-auto  rounded-lg  gap-2 p-3 h-full ${fileBg}`} style={style}>
            <Avatar img={imgSrc} alt={'image'} size={imageSize} />
        </div>
    ) : (
        <div className={`flex gap-2 p-2  ${fileBg} rounded-lg m-auto ${iconOnly ? "w-fit" : width}`} style={style}>
            <div className="text-2xl bg-gray-400  text-gray-200 p-2 rounded-lg md:text-3xl">
                {getIconForFile(fileName)}
            </div>
            {!iconOnly && <div className="self-center text-gray-700 break-all truncate">{fileName}</div>}
        </div>
    );
};
