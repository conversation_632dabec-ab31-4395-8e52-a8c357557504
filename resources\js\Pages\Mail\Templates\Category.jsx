import useFetch from "@/Global/useFetch";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { router } from "@inertiajs/react";
import { <PERSON><PERSON>, Button, Modal } from "flowbite-react";
import { useState, useEffect, useCallback, memo } from "react";
import { CgSpinner } from "react-icons/cg";
import { MdDeleteOutline, MdOutlineAdd, MdOutlineEdit } from "react-icons/md";
import AddCategory from "./AddCategory";
import EditCategory from "./EditCategory";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";

const Category = memo(function Category({ selectedCategory, setSelectedCategory }) {
    const [reFetch, setReFetch] = useState(0);

    const { data: cateData, loading: cateDataLoading } = useFetch(route('mail.template.category.index', { all: true }), reFetch);
    const [collection, setCollection] = useState();

    useEffect(() => {
        setCollection(cateData?.collection);
    }, [cateData]);

    const [modalState, setModalState] = useState({ openAdd: false, openEdit: false, selected: null });
    const [selectedCategories, setSelectedCategories] = useState([]);
    const [isMultipleDeleteConfirmOpen, setIsMultipleDeleteConfirmOpen] = useState(false);

    const handleCategorySelect = useCallback((categoryId) => {
        setSelectedCategory(categoryId);
    }, [setSelectedCategory]);

    const handleMultipleDelete = useCallback((res) => {
        if (res) {
            router.delete(route("mail.template.category.destroy", { category: selectedCategories.toString() }));
            setReFetch(prev => prev + 1);
        }
        setSelectedCategories([]);
        setIsMultipleDeleteConfirmOpen(false);
    }, [selectedCategories]);

    const handleModalStateChange = useCallback((newState) => {
        setModalState(newState);
    }, []);

    const handleRefetch = useCallback(() => {
        setReFetch(prev => prev + 1);
    }, []);

    return (
        <div className="w-full bg-white rounded-md h-fit">
            <div className="flex items-center justify-between gap-3 px-3 py-2">
                <span>Categories</span>
                <div className="">


                    {(collection && collection?.can_add) &&
                        <Button theme={button_custom} color="gray" size="xs" onClick={() => handleModalStateChange({ ...modalState, openAdd: true })}>
                            <div className="flex items-center">
                                <MdOutlineAdd className="text-slate-500" />
                                <span className="text-xs">Add</span>
                            </div>
                        </Button>
                    }
                </div>
            </div>
            <div className={`flex items-center justify-between border-b text-sm px-1.5 ${selectedCategory === "0" || selectedCategory === 0 ? "bg-blue-400 text-white" : "hover:bg-blue-200 bg-white"}`}>
                <Button
                    className="justify-start w-full bg-transparent border-0"
                    color="gray"
                    size="sm"
                    onClick={() => handleCategorySelect("0")}
                >
                    <div className="flex items-center gap-2">All</div>
                </Button>
            </div>
            <div className="flex flex-col h-full overflow-y-auto bg-white lg:flex-col md:flex-col">
                {cateDataLoading == false ? (
                    collection?.categories.map((category, key) => (
                        <div key={key}
                            className={`flex items-center justify-between border-b text-sm
                            ${category.id == selectedCategory ? "bg-blue-400 text-white" : "hover:bg-blue-200 bg-white"}`}
                        >
                            <div className={`${(collection?.can_edit || collection?.can_delete) ? " w-full " : " w-4/5 "}"`}>
                                <Button
                                    className="justify-start bg-transparent border-0"
                                    color="gray"
                                    size="sm"
                                    onClick={() => handleCategorySelect(category.id)}
                                    theme={{
                                        "inner": {
                                            "base": "flex w-full transition-all duration-200"
                                        }
                                    }}
                                >
                                    <div className="flex justify-between w-full gap-2 overflow-y-auto text-start">
                                        <div className="w-2/3 truncate"> {category.name} </div>
                                        <Badge color="info"> {category.templates_count} </Badge>
                                    </div>
                                </Button>
                            </div>

                            {(collection?.can_edit || collection?.can_delete) &&
                                <div className="flex justify-end w-1/5 gap-2">

                                    {collection?.can_edit &&
                                        <Button theme={button_custom} color="withoutBorder" size="xxs" onClick={() => handleModalStateChange({ openEdit: true, selected: category })}>
                                            <MdOutlineEdit className={category.id == selectedCategory ? "text-white" : "text-slate-400"} />
                                        </Button>
                                    }
                                    {collection?.can_delete &&
                                        <Button theme={button_custom} color="withoutBorder" size="xxs"
                                            onClick={() => {
                                                setSelectedCategories([category.id]);
                                                setIsMultipleDeleteConfirmOpen(true);
                                            }}>
                                            <MdDeleteOutline className={category.id == selectedCategory ? "text-white" : "text-slate-400"} />
                                        </Button>
                                    }
                                </div>
                            }
                        </div>
                    ))
                ) : (
                    <div className="flex justify-center p-2">
                        <CgSpinner className="text-3xl text-blue-400 ease-in-out animate-spin" />
                    </div>
                )}
            </div>
            {
                modalState.openAdd && (
                    <Modal show={modalState.openAdd} size="md" onClose={() => handleModalStateChange({ ...modalState, openAdd: false })} popup>
                        <Modal.Header className="p-2">
                            <h4 className="text-lg">Add Category</h4>
                        </Modal.Header>
                        <Modal.Body className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <AddCategory onClose={() => handleModalStateChange({ ...modalState, openAdd: false })} setReFetch={handleRefetch} />
                        </Modal.Body>
                    </Modal>
                )
            }
            {
                modalState.openEdit && (
                    <Modal show={modalState.openEdit} size="md" onClose={() => handleModalStateChange({ ...modalState, openEdit: false })} popup>
                        <Modal.Header className="p-2">
                            <h4 className="text-lg">Edit Category</h4>
                        </Modal.Header>
                        <Modal.Body className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <EditCategory onClose={() => handleModalStateChange({ ...modalState, openEdit: false })} category={modalState.selected} setReFetch={handleRefetch} />
                        </Modal.Body>
                    </Modal>
                )
            }
            {
                isMultipleDeleteConfirmOpen && (
                    <ConfirmBox
                        isOpen={isMultipleDeleteConfirmOpen}
                        onClose={() => setIsMultipleDeleteConfirmOpen(false)}
                        onAction={handleMultipleDelete}
                        title="Delete Category"
                        message="Do you want to delete the selected category?"
                        confirmText="Yes, Delete!"
                        cancelText="No, Keep It"
                        confirmColor="orange"
                        cancelColor="gray"
                    />
                )
            }
        </div >
    );
});

export default Category;
