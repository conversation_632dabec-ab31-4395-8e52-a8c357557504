<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\User;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new User())->getTable();
    }
    /**
     * Display a listing of the resource.
     */

    public function index()
    {

        $search = request()->query('search', null);
        $perPage = request()->query('perPage', 10);
        $column = request()->query('sortColumn', 'id');
        $direction = request()->query('sortBy', 'desc');
        $data['users'] = User::query();

        $data['users'] = $data['users']->orderBy($column, $direction)
            ->with('roles','parent')
            ->when($search, function ($query, $search) {
                return $query->whereAny(['body', 'file', 'id'], 'LIKE', "%$search%");
            });
        if (!request()->user()->isAdmin()) {
            $data['users'] = $data['users']->where(function ($query) {
                $query->where('parentUser_id', Auth::id())
                    ->orWhere('id', Auth::id())
                    ->orWhereIn('parentUser_id', function ($subQuery) {
                        $subQuery->select('id')
                            ->from((new User())->getTable())
                            ->where('parentUser_id', Auth::id());
                    });
            });
        }
        $data['users'] = $data['users']->paginate($perPage)->withQueryString();

        $data['getData'] = $_GET;

        return (request()->header('Accept') == 'application/json') ?
            response()->json(['status' => true, 'collection' => $data, 'message' => 'Messages fetched successfully']) :
            Inertia::render('Admin/Users/<USER>', [
                'collection' => $data
            ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $r)
    {
         $r->validate(
            [
                'name' => 'required|string|max:255|unique:user,username',
                'password' => 'required|string|min:8',
                'email' => 'required|email|max:255',
                'mobile' => 'required|string|max:20',
                'dateOfBirth' => 'nullable|date',
                'officeEmail' => 'nullable|email|max:255',
                'userLanguage' => 'nullable|string|max:50',
                'phoneAccountID' => 'nullable|string|max:50',
                'reportsTo' => 'nullable|integer',
                'roleId' => 'nullable|integer',
                'auditorId' => 'nullable|integer',
            ],
            [],
            [
                'name' => 'Name',
                'password' => 'Password',
                'email' => 'E-Mail',
                'mobile' => 'Mobile',
                'dateOfBirth' => 'Date of Birth',
                'officeEmail' => 'Office E-Mail',
                'userLanguage' => 'Language',
                'phoneAccountID' => 'Phone Account Id',
                'reportsTo' => 'Reports To',
                'roleId' => 'Role Id',
                'auditorId' => 'Auditor Id',
            ]

        );


        $user = new User();
        $user->username = $r->name;
        $user->password = $r->password;
        // $user->role_id = $r->roleId;
        $user->isActive = $r->isActive;
        $user->parentUser_id = Auth::id();
        $user->mobile = $r->mobile;
        $user->email = $r->email;
        $user->save();

        $rolesID = explode(',', $r->roleId);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(1);
        $roles = Role::whereIn('id', $rolesID)->get();
        // $user->($roles);
        $user->assignRole($roles);
       

        // pending db update
        // if ($r->hasFile('attachment')) {
        //     foreach ($r->attachment as $file) {
        //         $fileName = time() . '-' . $file->getClientOriginalName();
        //         $path = storeFile('uploads/User', $file, $fileName);
        //         $user->file = $path;
        //     }
        // }

        // $user->dateLastSync = $r->dateLastSync;
        // $user->phoneAccount_id = $r->phoneAccountID;
        // $user->dateOfBirth = $r->dateOfBirth;
        // $user->reportsTo_id = $r->reportsTo;
        // $user->officeEmail = $r->officeEmail;
        // $user->officeMobile = $r->officeMobile;
        // $user->biometricEmpCode = $r->biometricEmpCode;
        // $user->userLanguage = $r->userLanguage;


        return (request()->header('Accept') == 'application/json') ?
            response()->json(['status' => true, 'message' => 'User added successfully']) :
            redirect()->back()
            ->with(['message' => 'User added successfully', 'type' => 'success']);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data['user'] = User::find($id);
        $data['roles'] = $data['user']->roles;
        return response()->json(['status' => true, 'collection' => $data]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $r, string $id)
    {

        $r->validate(
            [
                'name' => 'required|string|max:255|unique:user,username,' . $id . ',id',
                // 'password' => 'required|string|min:8',
                'email' => 'required|email|max:255',
                'mobile' => 'required|string|max:20',
                'dateOfBirth' => 'nullable|date',
                'officeEmail' => 'nullable|email|max:255',
                'userLanguage' => 'nullable|string|max:50',
                'phoneAccountID' => 'nullable|string|max:50',
                'reportsTo' => 'nullable|integer',
                'roleId' => 'nullable|integer',
                'auditorId' => 'nullable|integer',
            ],
            [],
            [
                'name' => 'Name',
                'password' => 'Password',
                'email' => 'E-Mail',
                'mobile' => 'Mobile',
                'dateOfBirth' => 'Date of Birth',
                'officeEmail' => 'Office E-Mail',
                'userLanguage' => 'Language',
                'phoneAccountID' => 'Phone Account Id',
                'reportsTo' => 'Reports To',
                'roleId' => 'Role Id',
                'auditorId' => 'Auditor Id',
            ]

        );

        try {
            if ($id == 1) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['status' => false, 'message' => 'Edit  Not Permitted']) :
                    redirect()->back()->with(['message' => 'Edit Not Permitted', 'type' => 'failure']);
            }

            $user = User::find($id);
            if ($user) {
                $user->username = $r->name;
                // $user->password = $r->password ?? $user->password;
                // $user->role_id = $r->roleId;
                $user->isActive = $r->isActive;
                $user->mobile = $r->mobile;
                $user->email = $r->email;
                $rolesID = explode(',', $r->roleId);

                $roles = Role::whereIn('id', $rolesID)->get();
                $user->assignRole($roles);
                $user->save();

                // pending db update
                // if ($r->hasFile('attachment')) {
                //     if ($user->file) {
                //         deleteFile($user->file);
                //     }
                //     foreach ($r->attachment as $file) {
                //         $fileName = time() . '-' . $file->getClientOriginalName();
                //         $path = storeFile('uploads/User', $file, $fileName);
                //         $user->file = $path;
                //     }
                // }

                // $user->dateLastSync = $r->dateLastSync;
                // $user->phoneAccount_id = $r->phoneAccountID;
                // $user->dateOfBirth = $r->dateOfBirth;
                // $user->reportsTo_id = $r->reportsTo;
                // $user->officeEmail = $r->officeEmail;
                // $user->officeMobile = $r->officeMobile;
                // $user->biometricEmpCode = $r->biometricEmpCode;
                // $user->userLanguage = $r->userLanguage;

            } else {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['status' => false, 'message' => 'User Not Found.']) :
                    redirect()->back()
                    ->with(['message' => 'User Not Found', 'type' => 'failure']);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'User Updates successfully']) :
                redirect()->back()

                ->with(['message' => 'User Updates successfully', 'type' => 'success']);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $ids = explode(',', $id);
            if (($key = array_search(1, $ids)) !== false) {
                unset($ids[$key]);
            }
            $keyword = User::whereIn('id', $ids)->get();
            if ($keyword->count() == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'User not found.', 'status' => false]) :
                    redirect()->back()->with(["message" => 'User not found.', "type" => "failure"]);
            }
            User::whereIn('id', $ids)->delete();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'User deleted successfully.']) :
                redirect()->back()->with(['message' => 'User deleted successfully.', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
