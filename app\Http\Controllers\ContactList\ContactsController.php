<?php

namespace App\Http\Controllers\ContactList;

use Exception;
use PDOException;
use Inertia\Inertia;
use App\Models\Contact;
use App\Models\ContactList;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class ContactsController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new ContactList())->getTable();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }
        return Inertia::render('ContactList/Add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }
        $validated = $request->validate(
            [
                'mobile' => 'required_without:email',
                'email' => 'required_without:mobile',
                'contact_list_id' => 'required'
            ],
            [
                'mobile.required_without' => 'Mobile Field Required',
                'email.required_without' => 'Email Field Required'

            ],
            [
                'mobile' => 'Mobile',
                'email' => 'Email',
                'contact_list_id' => 'contact list id'
            ]
        );

        try {
            $emails = explode("\n", $request->email);
            $validEmail = validateEmails($emails);
            $filteredNumbersArray = array_filter(mobileFilter($request->mobile));
            if (count($filteredNumbersArray) == 1 || count($validEmail['valid']) == 1) {
                $validator = Validator::make(
                    $request->all(),
                    [
                        'mobile' => 'max:15',
                    ],
                    [],
                    [
                        'mobile' => 'Mobile',
                        'email' => 'Email'
                    ]
                );

                if ($validator->fails()) {
                    return (request()->header('Accept') == 'application/json') ?
                        response()->json(['errors' => $validator->errors(), 'status' => false])
                        : redirect()->back()->withErrors($validator->errors());
                }
                Contact::insert([
                    [
                        "name" => $request->name,
                        "mobile" => $filteredNumbersArray[0] ?? null,
                        "var1" => $request->var1 ?? null,
                        "var2" => $request->var2 ?? null,
                        "var3" => $request->var3 ?? null,
                        "var4" => $request->var3 ?? null,
                        "var5" => $request->var3 ?? null,
                        "contact_list_id" => $request->contact_list_id,
                        "user_id" => Auth::id(),
                        "email" => $validEmail['valid'][0] ?? null,
                    ]
                ]);

                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Contact Added SuccessFully", 'status' => true])
                    : redirect()->back()->with(["message" => "Contact Added SuccessFully", "type" => "success"]);
            }
            if (count($filteredNumbersArray) == 0 && count($validEmail['valid']) == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Invalid contacts or empty !!", 'status' => true])
                    :
                    redirect()->back()->with(["message" => "Invalid contacts or empty !!", "type" => "failure"]);
            }
            $bulkData = [];
            $i = 0;
            foreach ($filteredNumbersArray as $value) {
                $bulkData[$i] =
                    [
                        "name" => $request->name,
                        "mobile" => $value,
                        "var1" => $request->var1 ?? null,
                        "var2" => $request->var2 ?? null,
                        "var3" => $request->var3 ?? null,
                        "var4" => $request->var3 ?? null,
                        "var5" => $request->var3 ?? null,
                        "contact_list_id" => $request->contact_list_id,
                        "user_id" => Auth::id(),
                        "email" => null,

                    ];
                $i++;
            }
            $j = 0;
            foreach ($validEmail['valid'] as $value) {
                $bulkData["a-$j"] =
                    [
                        "name" => $request->name,
                        "email" => $value,
                        "mobile" => null,
                        "var1" => $request->var1 ?? null,
                        "var2" => $request->var2 ?? null,
                        "var3" => $request->var3 ?? null,
                        "var4" => $request->var3 ?? null,
                        "var5" => $request->var3 ?? null,
                        "contact_list_id" => $request->contact_list_id,
                        "user_id" => Auth::id(),
                    ];
                $j++;
            }
            Contact::insert($bulkData);
            return redirect()->back()->with(["message" => "Contacts Added SuccessFully", "type" => "success"]);
        } catch (Exception $e) {
            return redirect()->back()->with(["type" => "failure", "message" => errorMessage(3)]);
        }
    }


    public function storeApi(Request $request)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        $request->validate(['contact_list_id' => 'required', 'contacts' => 'required']);
        $contacts = json_decode($request->contacts, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return response()->json([
                "status" => false,
                'message' => 'Invalid JSON in contacts'
            ], 400);
        }
        if (count($contacts) > 50) {
            return response()->json([
                "status" => false,
                'message' => 'Max 50 Record at once allowed given ' . count($contacts) . '.',
            ], 500);
        }

        $rules = [
            'email' => 'nullable|email|required_without:mobile',
            'mobile' => 'nullable|string|required_without:email',
        ];

        $validContacts = [];
        $invalidContacts = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($contacts as $contact) {
            $validator = Validator::make($contact, $rules);

            if ($validator->fails()) {
                $invalidContacts[] = $contact;
                $failCount++;
            } else {
                $validContacts[] = $contact;
                $successCount++;
            }
        }



        if (!empty($validContacts)) {
            try {
                $updatedContacts = collect($validContacts)->map(function ($contact) use ($request) {
                    $contact['contact_list_id'] = $request->contact_list_id;
                    $contact['user_id'] = $request->user()->id;
                    return $contact;
                });
                Contact::insert($updatedContacts->toArray());

                return response()->json([
                    "status" => true,
                    'message' => 'Contact(s) Created Successfully.',
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'invalidContacts' => $invalidContacts
                ], 200);
            } catch (PDOException $e) {
                return response()->json([
                    "status" => false,
                    'message' => errorMessage($e->errorInfo[1]),
                ], 500);
            }
        } else {
            return response()->json([
                "status" => false,
                'message' => 'No valid contacts to insert. the record contain either mobile or email value.',
                'success_count' => $successCount,
                'fail_count' => $failCount
            ], 422);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
        $request->validate([
            'mobile' => 'required_if:email,null|max:15',
            'email' => 'required_if:mobile,null',
        ]);
        try {
            Contact::updateOrCreate(["id" => $id], $request->all());

            $message = "Contact Updated SuccessFully";
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => true])
                : redirect()->back()->with(["message" => $message, "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage(), 'status' => false])
                : redirect()->back()->with(["type" => "failure", "message" => errorMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }
        try {
            $ids = explode(",", $id);
            $userIds = request()->user()->getAllChildUserIds();
            $contacts = Contact::whereIn('user_id', $userIds)->whereIn('id', $ids)->get();
            $status = true;
            $message = "Record(s) Deleted !!";
            if ($contacts->count()) {
                Contact::whereIn('id', $ids)->delete();
            } else {
                $message = "Nothing to Delete.";
            }

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => $message, "status" => $status])
                : redirect()->back()->with(["message" => $message, "type" => "success"]);
        } catch (Exception $e) {
            $message = errorMessage();
            if ($e->getCode() == 23000) {
                $message = errorMessage(5);
            }
            if (request()->header('Accept') == 'application/json') {
                return response()->json(["message" => $message, "status" => false]);
            }
            return redirect()->back()->with(["message" => $message, "type" => "failure"]);
        }
    }

    public function getContactTableFields()
    {

        return
            response()->json([
                'columns' =>
                array_values(array_diff(DB::connection()->getSchemaBuilder()->getColumnListing('contact'), ["id", "contact_list_id", "user_id", "isOnWhatsapp"]))
            ]);
    }
    public function storeAppendValue(Request $request)
    {
        // return($request);
        $request->validate([
            'contactListId' => 'required',
            'columnName' => 'required',
            'preFix' => 'required_without:postFix',
            'postFix' => 'required_without:preFix'
        ]);
        try {
            DB::table('contact')
                ->where('contact_list_id', $request->contactListId)
                ->update([
                    $request->columnName => DB::raw(
                        "CONCAT(" .
                            ($request->preFix != "" ? "'$request->preFix', " : "") .
                            "$request->columnName" .
                            ($request->preFix == "" ? ", '$request->postFix'" : "") .
                            ")"
                    )
                ]);
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Contact List Updated", "status" => true])
                :
                redirect()->back()->with(["message" => "Contact List Updated", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
