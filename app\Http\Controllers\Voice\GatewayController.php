<?php

namespace App\Http\Controllers\Voice;

use App\Http\Controllers\Controller;
use App\Models\Voice\Gateway;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class GatewayController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new Gateway())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        
        try {
            $totalCount = request()->query('perPage', 10);
            $column = request()->query('column', 'id');
            $sortBy = request()->query('sort', 'desc');
            $search = request()->query('search');
            $status = request()->query('status');
            $data['getData'] = request()->query();

            $getUser = request()->query('userID');

            $data["gateways"] = Gateway::query();
            $data["gateways"] = $data["gateways"]->orderBy($column, $sortBy);

            if ($getUser) {
                $user = User::find($getUser);
                $userIds = request()->user()->getAllChildUserIds($getUser);
                $data["gateways"] = $data["gateways"]->orderBy($column, $sortBy);
                if (request()->user()->isAdmin() || in_array($user->id, $userIds->toArray())) {
                    $data["gateways"] = $data["gateways"]->whereIn('user_id', $userIds);
                } else {
                    return (request()->header('Accept') == 'application/json')
                        ? response()->json(['message' => 'Invalid request.', 'status' => true])
                        : redirect()->back()->with(["message" => 'Invalid request.', "type" => "failure"]);
                }
            } else {
                $userIds = request()->user()->getAllChildUserIds();
                $data["gateways"] = $data["gateways"]->whereIn("user_id", $userIds);
            }
            if ($status != null) {
                $data["gateways"] = $data["gateways"]->where('status', $status);
            }

            $data["gateways"] = $data["gateways"]->with('user');
            
            $data["gateways"] = $data["gateways"]->when($search, function ($query, $search) {
                return $query->whereAny(['name', 'id'], 'like', "%$search%");
            })->paginate($totalCount)->withQueryString();

            // $data["can_add"] = request()->user()->can($this->tableName . ".add");
            // $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
            // $data["can_delete"] = request()->user()->can($this->tableName . ".delete");

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Gateway List.", 'status' => true, 'collection' => $data])
                : Inertia::render('Voice/Gateways/Index', ['collection' => $data]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }

        // return Inertia::render('Voice/Gateways/Index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $rules = [
            'name' => "required|string|max:100|unique:{$this->tableName},name",
            'type' => ['required', Rule::in(['imap', 'smtp'])],
            'username' => 'required|string|max:250',
            'port' => 'required|numeric',
            'password' => 'required|string|min:8',
            'limit' => 'required|numeric',
            'host' => 'required|string|max:100',
            'email_from' => 'nullable|string|max:200|email',
            'auth_type' => 'required|string|max:50',
        ];
        $request->validate($rules, [], [
            'username' => 'Username',
            'password' => 'Password',
            'name' => 'Name',
            'type' => 'Type',
            'host' => 'Host',
            'port' => 'Port',
            'email_from' => 'Email From',
            'auth_type' => 'Authentication Type',
            'limit' => 'Limit',
        ]);

        try {
            if ($request->has('id')) {
                $gateway = Gateway::find($request->id);
            } else {
                $gateway = new Gateway();
            }
            $gateway->username = $request->username;
            $gateway->password = $request->password;
            $gateway->name = $request->name;
            $gateway->auth_type = $request->type;
            $gateway->host = $request->host;
            $gateway->port = $request->port;
            $gateway->limit_per_minute = $request->limit;
            $gateway->email_from = $request->email_from;
            $gateway->user_id = Auth::id();
            $gateway->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Gateway Created.", "status" => true])
                : redirect()->back()->with(["message" => "Gateway Created.", "type" => "success"]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }

        //  name, type, username, port, server, password, user_id
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        
        $request->validate(
            [
                'name' => "required|string|max:100|unique:{$this->tableName},name,{$id}",
                'type' => ['required', Rule::in(['imap', 'smtp'])],
                'username' => 'required|string|max:250',
                'port' => 'required|numeric',
                'password' => 'required|string|min:8',
                'limit' => 'required|numeric',
                'host' => 'required|string|max:100',
                'email_from' => 'nullable|string|max:200|email',
                'auth_type' => 'required|string|max:50',
            ],
            [],
            [
                'username' => 'Username',
                'password' => 'Password',
                'name' => 'Name',
                'type' => 'Type',
                'host' => 'Host',
                'port' => 'Port',
                'email_from' => 'Email From',
                'auth_type' => 'Authentication Type',
                'limit' => 'Limit',
            ]
        );

        try {
            $gateway = Gateway::find($request->id);

            $gateway->username = $request->username;
            $gateway->password = $request->password;
            $gateway->name = $request->name;
            $gateway->auth_type = $request->type;
            $gateway->host = $request->host;
            $gateway->port = $request->port;
            $gateway->limit_per_minute = $request->limit;
            $gateway->email_from = $request->email_from;
            $gateway->user_id = Auth::id();
            // $gateway->isActive = $request->isActive;
            $gateway->update();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Gateway Updated.", "status" => true])
                : redirect()->back()->with(["message" => "Gateway Updated.", "type" => "success"]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {

        try {
            $userIds = request()->user()->getAllChildUserIds();
            $ids = explode(",", $id);

            $gateways = Gateway::whereIn('id', $ids)->whereIn('user_id', $userIds->toArray())->get();

            if ($gateways->isNotEmpty()) {
                Gateway::whereIn('id', $ids)->whereIn('user_id', $userIds->toArray())->delete();
                $message = "Gateway deleted.";
                $status = true;
            } else {
                $message = "Gateway Not Found.";
                $status = false;
            }

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => $status])
                : redirect()->back()->with(["message" => $message, "type" => $status ? "success" : "failure"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
