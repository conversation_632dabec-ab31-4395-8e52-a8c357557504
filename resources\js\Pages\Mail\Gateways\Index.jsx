import { But<PERSON>, Checkbox, Drawer, Table, ToggleSwitch } from "flowbite-react";
import { useState } from "react";

import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import { button_custom, customDrawerEdit, table_custom } from "@/Pages/Helpers/DesignHelper";
import { changeFlag } from "@/Pages/Helpers/Helper";
import { router } from "@inertiajs/react";
import { lazy } from 'react';
import { IoMdAdd } from "react-icons/io";
import { MdDeleteOutline, MdOutlineEdit } from "react-icons/md";
const MailMain = lazy(() => import("../MailMain"));
const Add = lazy(() => import("./Add"));
const Edit = lazy(() => import("./Edit"));

export default function Index({ collection }) {
    const { gateways, getData, can_add, can_edit, can_delete } = collection;
    const baseRoute = "mail";
    // ----------------Edit --------------------
    const currentPageRoute = baseRoute + ".gateways.index";
    const [isAddOpen, setIsAddOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [selected, setSelected] = useState();
    const [selectedGateways, setSelectedGateways] = useState([]);
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const [activeStatusChangeLoader, SetActiveStatusChangeLoader] = useState(false);

    const handleCheckboxChange = (id) => {
        setSelectedGateways((prevSelected) =>
            prevSelected.includes(id)
                ? prevSelected.filter((gwId) => gwId !== id) // Deselect if already selected
                : [...prevSelected, id] // Add if not selected
        );
    };

    // Handle "Select All" Checkbox
    const handleSelectAll = (e) => {
        if (e.target.checked) {
            if (selectedGateways.length === gateways.length) {
                setSelectedGateways([]);
            } else {
                setSelectedGateways(gateways?.data?.map((gateway) => gateway.id));
            }
        } else {
            setSelectedGateways([]);
        }
    };

    function deleteRecords() {
        if (selectedGateways.length > 0) {
            router.delete(route(baseRoute + ".gateways.destroy", { gateway: selectedGateways.toLocaleString() }), { preserveState: true, preserveScroll: true });
        }
    }

    const handleMultipleDelete = (res) => {
        if (res) {
            deleteRecords();
        }
        // Reset checkboxes and state
        setSelectedGateways([]);
        SetIsMultipleDeleteConfirmOpen(false);
    };

    return (
        <MailMain>
            <>
                <div className="pb-2 rounded-lg">
                    <div className="h-fit bg-white rounded  overflow-auto w-full my-2.5">
                        {(can_delete || can_add) && (
                            <div className="flex justify-between p-2">
                                {can_delete && (
                                    <Button
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() => selectedGateways.length > 0 && SetIsMultipleDeleteConfirmOpen(true)}
                                        disabled={selectedGateways.length === 0 || router.processing}
                                    >
                                        <MdDeleteOutline className="text-slate-500" /> {/* Consistent Icon */}
                                        <span className="text-xs">Delete</span>
                                    </Button>
                                )}
                                {can_add && (
                                    <Button
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() => setIsAddOpen(true)}
                                        disabled={router.processing}
                                    >
                                        <IoMdAdd className="text-xs text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </Button>
                                )}
                            </div>
                        )}
                        <div className="h-full">
                            <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                                <Table hoverable theme={table_custom}>
                                    <Table.Head >
                                        {can_delete &&
                                            <Table.HeadCell className="w-fit">
                                                <Checkbox
                                                    color="blue" onChange={handleSelectAll}
                                                    checked={gateways?.data?.length > 0 && selectedGateways.length === gateways?.data?.length}
                                                    disabled={router.processing}
                                                />
                                            </Table.HeadCell>
                                        }
                                        <Table.HeadCell>
                                            <SortLink showName={'Name'} routeName={currentPageRoute} column={'name'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Host'} routeName={currentPageRoute} column={'host'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Email'} routeName={currentPageRoute} column={'username'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Port'} routeName={currentPageRoute} column={'port'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'User'} routeName={currentPageRoute} column={'user_id'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Limit'} routeName={currentPageRoute} column={'limit_per_minute'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Active'} routeName={currentPageRoute} column={'isActive'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        {(can_delete || can_edit) &&
                                            <Table.HeadCell>Action</Table.HeadCell>
                                        }
                                    </Table.Head>
                                    <Table.Body className="divide-y">
                                        {gateways?.data?.map((gateway) =>
                                            <Table.Row key={'gateway-' + gateway.id}>
                                                {can_delete &&
                                                    <Table.Cell className="w-fit">
                                                        <Checkbox
                                                            color={"blue"} className="rowCheckBox"
                                                            checked={selectedGateways.includes(gateway.id)}
                                                            onChange={() => handleCheckboxChange(gateway.id)}
                                                            disabled={router.processing}
                                                        />
                                                    </Table.Cell>
                                                }
                                                <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {gateway.name ?? '-'}
                                                </Table.Cell>
                                                <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {gateway.host ?? '-'}
                                                </Table.Cell>
                                                <Table.Cell>{gateway.username ?? '-'}</Table.Cell>
                                                <Table.Cell>
                                                    {gateway.port ?? '-'}
                                                </Table.Cell>
                                                <Table.Cell>{gateway.user.username ?? '-'}</Table.Cell>
                                                <Table.Cell> {gateway.limit_per_minute ?? '-'} / Minute</Table.Cell>

                                                <Table.Cell>
                                                    {can_edit ?
                                                        <ToggleSwitch sizing="sm" color="cyan" checked={gateway.isActive} onChange={() => changeFlag('mail_gateway', 'isActive', gateway.id, !gateway.isActive, SetActiveStatusChangeLoader)} disabled={activeStatusChangeLoader} />
                                                        :
                                                        <ToggleSwitch sizing="sm" color="cyan" checked={gateway.isActive} disabled={true} />
                                                    }
                                                </Table.Cell>
                                                {(can_delete || can_edit) &&
                                                    <Table.Cell>
                                                        <div className="flex gap-2">
                                                            {can_edit &&
                                                                <Button
                                                                    theme={button_custom}
                                                                    color="success"
                                                                    size="xs"
                                                                    onClick={() => { setIsEditOpen(true); setSelected(gateway) }}
                                                                    disabled={router.processing}
                                                                >
                                                                    <MdOutlineEdit className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        Edit
                                                                    </span>
                                                                </Button>
                                                            }
                                                            {can_delete &&
                                                                <Button
                                                                    theme={button_custom}
                                                                    color="failure"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        SetIsMultipleDeleteConfirmOpen(!isMultipleDeleteConfirmOpen);
                                                                        setSelectedGateways([gateway.id]);
                                                                    }}
                                                                    disabled={router.processing}
                                                                >
                                                                    <MdDeleteOutline className="text-sm" /> {/* Consistent Icon */}
                                                                    <span className="text-xs ms-1">
                                                                        Delete
                                                                    </span>
                                                                </Button>
                                                            }
                                                        </div>
                                                    </Table.Cell>
                                                }
                                            </Table.Row>
                                        )}
                                    </Table.Body>
                                </Table>
                            </div>
                        </div>
                    </div>
                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={getData ?? null}
                                    routeName={baseRoute + ".gateways.index"}
                                    data={gateways}
                                    idName={"name"}
                                // id={target}
                                />
                            </div>

                            <Paginate tableData={gateways} />
                        </div>
                    </div>
                </div>

                {isAddOpen &&
                    <Drawer open={isAddOpen} onClose={() => setIsAddOpen(!isAddOpen)} position="right" className="w-full lg:w-1/3 md:w-full" theme={customDrawerEdit}>
                        <Drawer.Header title="Add Gateway (Email Configuration)" />
                        <Drawer.Items>
                            <div className="flex items-center justify-between px-4 py-2 mb-3 bg-white rounded-lg">
                                <Add onClose={() => setIsAddOpen(!isAddOpen)} />
                            </div>
                        </Drawer.Items>
                    </Drawer>
                }
                {isEditOpen &&
                    <Drawer open={isEditOpen} onClose={() => setIsEditOpen(!isEditOpen)} position="right" className="w-1/2" theme={customDrawerEdit}>
                        <Drawer.Header title="Edit Email Server Configuration" />
                        <Drawer.Items>
                            <div className="flex items-center justify-between px-4 py-2 mb-3 bg-white rounded-lg">
                                <Edit onClose={() => setIsEditOpen(!isEditOpen)} gateway={selected} />
                            </div>
                        </Drawer.Items>
                    </Drawer>
                }
                {isMultipleDeleteConfirmOpen &&
                    <ConfirmBox isOpen={isMultipleDeleteConfirmOpen}
                        onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                        onAction={handleMultipleDelete} // Handle the user's choice
                        title="Delete Gateway "
                        message="Do you want to Delete gateway."
                        confirmText="Yes, Delete!"
                        cancelText="No, Keep It"
                        confirmColor="orange"
                        cancelColor="gray"
                        confirmDisabled={router.processing} // Disable confirm button while processing
                    />
                }
            </>
        </MailMain >
    );
}
