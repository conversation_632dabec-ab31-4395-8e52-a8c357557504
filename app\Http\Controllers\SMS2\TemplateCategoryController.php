<?php

namespace App\Http\Controllers\SMS2;

use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\SMS2\{Template as WaBaTemplate, TemplateCategory as WaBaTemplateCategory};

class TemplateCategoryController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBaTemplateCategory())->getTable();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!request()->user()->can("$this->tableName.index")) {
            abort(403, "unauthorized");
        }

        try {
            $request = request();
            $userIds = request()->user()->getAllChildUserIds()??[];
            $data["categories"] = WaBaTemplateCategory::query();

            $data["categories"] = $data["categories"]->whereIn('user_id', $userIds);
            $data["categories"] = $data["categories"]->withCount('templates');

            $data['categories'] = $request->query('all', false)
                ? $data['categories']->get()
                : $data['categories']->paginate($request->query('perPage', 10))->withQueryString();

            $data += [
                "can_add" => request()->user()->can("$this->tableName.add"),
                "can_edit" => request()->user()->can("$this->tableName.edit"),
                "can_delete" => request()->user()->can("$this->tableName.delete"),
                "can_view" => request()->user()->can("$this->tableName.view")
            ];
            return response()->json(['collection' => $data, 'status' => true]);
        } catch (Exception $e) {
            
            return response()->json(['status' => false, 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can("$this->tableName.add")) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required|max:45",
        ]);
        try {
            $category = new WaBaTemplateCategory();
            $category->name = $request->name;
            $category->user_id = Auth::id();
            $category->save();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Category Added", "type" => "success"])
                : redirect()->back()->with(["message" => "Category Added", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "type" => "failure"])
                :
                redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id = null)
    {
        if (!request()->user()->can("$this->tableName.view")) {
            abort(403, "unauthorized");
        }

        try {
            $search = request()->query('search', null);
            $perPage = request()->query('perPage', 8);
            $column = request()->query('sortColumn', 'id');
            $direction = request()->query('sortBy', 'desc');
            $category = WaBaTemplateCategory::where(["user_id" => Auth::id(), 'id' => $id])->first();

            if ($id && !$category) {
                return response()->json(['message' => "Category not found.", 'status' => false]);
            }

            $templates = WaBaTemplate::orderBy($column, $direction)
                ->when($category, function ($query) use ($category) {
                    return $query->where("waba_template_category_id", $category->id);
                }, function ($query) {
                    return $query->whereHas('category', function ($query) {
                        $query->where('user_id', Auth::id());
                    });
                })
                ->when($search, function ($query) use ($search) {
                    return $query->whereAny(['name', 'body', 'datetime', 'waba_template_category_id'], 'LIKE', "%{$search}%");
                })
                ->paginate($perPage)->withQueryString();
            $getData = request()->query();
            return response()->json(["templates" => $templates, 'category' => $category, 'getData' => $getData, 'status' => true]);
        } catch (Exception $e) {
            return response()->json(['message' => errorMessage($e->getCode()), 'status' => false]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }
        $categories = WaBaTemplateCategory::where("user_id", Auth::id())->where("id", $id)->first();
        if (!$categories) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => "Category not found.", 'status' => false])
                : redirect()->back()->with(["message" => "Category not found.", "type" => "failure"]);
        }
        return
            response()->json(["categories" => $categories]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required|min:3|max:45",
        ]);
        try {
            $category = WaBaTemplateCategory::where(['user_id' => Auth::id(), 'id' => $request->id])->first();
            if (!$category) {
                return (request()->header('Accept') == 'application/json')
                    ? response()->json(['message' => "Category not found.", 'status' => false])
                    : redirect()->back()->with(["message" => "Category not found.", "type" => "failure"]);
            }
            $category->name = $request->name;
            $category->save();
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => "Category Updated", 'status' => true])
                : redirect()->back()->with(["message" => "Category Updated", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => ErrorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => ErrorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can("$this->tableName.delete")) {
            abort(403, "unauthorized");
        }
        $ids = explode(",", $id);

        try {
            $category = WaBaTemplateCategory::whereIn('id', $ids)->where('user_id', Auth::id())->get();

            if (count($category) == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Category not found.", 'status' => false])
                    : redirect()->back()->with(["message" => "Category not found.", "type" => "failure"]);
            }
            WaBaTemplateCategory::whereIn('id', $ids)->where('user_id', Auth::id())->delete();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Category deleted.", 'status' => true])
                : redirect()->back()->with(["message" => "Category deleted.", "type" => "success"]);
        } catch (Exception $e) {

            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
