<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\WaTemplate;
use App\Models\WaTemplateCategory;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TemplateCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaTemplateCategory())->getTable();
    }
    public function index()
    {
        try {
            if (!request()->user()->can("$this->tableName.view")) {
                abort(403, "unauthorized");
            }
            $userIds = request()->user()->getAllChildUserIds();

            $data["categories"] = WaTemplateCategory::query();
            $data["categories"] = $data["categories"]->whereIn('user_id', $userIds);
            $data["categories"] = $data["categories"]->withCount('template')->get();

            $data["can_add"] = request()->user()->can("$this->tableName.add");
            $data["can_view"] = request()->user()->can("$this->tableName.view");
            $data["can_edit"] = request()->user()->can("$this->tableName.edit");
            $data["can_delete"] = request()->user()->can("$this->tableName.delete");
            return response()->json(['collection' => $data, 'status' => true]);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can("$this->tableName.add")) {
            abort(403, "unauthorized");
        }
        $request->validate([
            "name" => "required|max:45",
        ]);
        try {
            $category = new WaTemplateCategory();
            $category->name = $request->name;
            $category->user_id = Auth::id();
            $category->save();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Category Added", "type" => "success"])
                :
                redirect()->back()->with(["message" => "Category Added", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "type" => "failure"])
                :
                redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id = null)
    {
        if (!request()->user()->can("$this->tableName.view")) {
            abort(403, "unauthorized");
        }

        try {
            $search = request()->query('search', null);
            $perPage = request()->query('perPage', 8);
            $column = request()->query('sortColumn', 'id');
            $direction = request()->query('sortBy', 'desc');
            $category = WaTemplateCategory::where(["user_id" => Auth::id(), 'id' => $id])->first();

            if ($id && !$category) {
                return response()->json(['message' => "Category not found.", 'status' => false]);
            }

            $templates = WaTemplate::orderBy($column, $direction)
                ->when($category, function ($query) use ($category) {
                    return $query->where("wa_template_category_id", $category->id);
                }, function ($query) {
                    return $query->whereHas('category', function ($query) {
                        $query->where('user_id', Auth::id());
                    });
                })
                ->when($search, function ($query) use ($search) {
                    return $query->whereAny(['name', 'body', 'datetime', 'wa_template_category_id'], 'LIKE', "%{$search}%");
                })
                ->paginate($perPage)->withQueryString();
            $getData = request()->query();
            return response()->json(["templates" => $templates, 'category' => $category, 'getData' => $getData, 'status' => true]);
        } catch (Exception $e) {
            return response()->json(['message' => errorMessage($e->getCode()), 'status' => false]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }
        $categories = WaTemplateCategory::where("user_id", Auth::id())->where("id", $id)->first();
        if (!$categories) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => "Category not found.", 'status' => false])
                : redirect()->back()->with(["message" => "Category not found.", "type" => "failure"]);
        }
        return
            response()->json(["categories" => $categories]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {

        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required|min:3|max:45",
        ]);
        try {
            $category = WaTemplateCategory::where(['user_id' => Auth::id(), 'id' => $request->id])->first();
            if (!$category) {
                return (request()->header('Accept') == 'application/json')
                    ? response()->json(['message' => "Category not found.", 'status' => false])
                    : redirect()->back()->with(["message" => "Category not found.", "type" => "failure"]);
            }
            $category->name = $request->name;
            $category->save();
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => "Category Updated", 'status' => true])
                : redirect()->back()->with(["message" => "Category Updated", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => ErrorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => ErrorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can("$this->tableName.delete")) {
            abort(403, "unauthorized");
        }

        $ids = explode(",", $id);
        $userIds = request()->user()->getAllChildUserIds();

        try {
            $category = WaTemplateCategory::whereIn('id', $ids)->whereIn('user_id', $userIds)->get();

            if (count($category) == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Category not found.", 'status' => false])
                    : redirect()->back()->with(["message" => "Category not found.", "type" => "failure"]);
            }

            WaTemplateCategory::whereIn('id', $ids)->whereIn('user_id', $userIds)->delete();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Category deleted.", 'status' => true])
                : redirect()->back()->with(["message" => "Category deleted.", "type" => "success"]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
