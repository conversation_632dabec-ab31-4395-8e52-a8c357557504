<?php

namespace App\Http\Controllers\SMS2;

use Exception;
use PDOException;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\{Auth, Storage};
use App\Models\SMS2\{Template as WaTemplate, TemplateCategory as WaTemplateCategory, TemplateView as WaTemplateView};

class TemplatesController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaTemplate())->getTable();
    }
    public function index()
    {
        $tableName=$this->tableName;
        if (!request()->user()->can("$tableName.view")) {
            abort(403, "unauthorized");
        }

        try {
            $search = request()->query('search', null);
            $totalCount = $_GET['perPage'] ?? 8;
            $backData['category_id'] = $_GET["category"] ?? null;
            $userIds = request()->user()->getAllChildUserIds()??[];
            $templates = WaTemplateView::query();
            $templates = $templates->whereIn("user_id", $userIds);

            if ($backData['category_id']) {
                $templates = $templates->where("sms_template_category_id", $backData['category_id']);
            }

            $templates = $templates->when($search, function ($query, $search) {
                return $query->where(function ($query) use ($search) {
                    $query->where('name', 'LIKE', "%$search%")
                        ->orWhere('id', 'LIKE', "%$search%")
                        ->orWhere('sms_template_category_name', 'LIKE', "%$search%")
                        ->orWhere('body', 'LIKE', "%$search%");
                });
            });

            $templates = $templates->orderBy('datetime', 'desc');
            $backData['templates'] = $templates->paginate($totalCount)->withQueryString();
            $backData['getData'] = $_GET;

            $backData["can_add"] = request()->user()->can("$tableName.add");
            $backData["can_edit"] = request()->user()->can("$tableName.edit");
            $backData["can_delete"] = request()->user()->can("$tableName.delete");


            return (request()->header('Accept') == 'application/json')
                ? response()->json(['collection' => $backData])
                : Inertia::render('SMS2/Templates/Index', ['collection' => $backData]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['status' => false, 'message' => errorMessage($e->getCode())])
                : redirect()->back()->with(['type' => "failure", 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $tableName=$this->tableName;
        if (!request()->user()->can("$tableName.add")) {
            abort(403, "unauthorized");
        }

        $request->validate(
            [
                'templateName' => 'required|max:45',
                'category_id' => 'required',
                'message' => 'bail|required_without:attachment',
                'attachment.*' => 'required_without:message|file|max:20000',
            ],
            [
                'msg.required_without' => "Please ensure that you provide either a message or a file upload, or both.",
                'files.required_without' => "Please ensure that you provide either a message or a file upload, or both."
            ],
            [
                "templateName" => 'Template Name',
                "msg" => 'Message',
                "category_id" => 'Category',
                "file.*" => "File",
                "file" => "Files",
                'attachment' => 'File',
                'message' => 'Message',
                'attachment.*' => 'File'
            ],
        );
        try {
            $data = $request->all();
            $fileName = null;
            $template = new WaTemplate();
            $template->name = $data["templateName"];
            $template->sms_template_category_id = $data["category_id"];
            $template->body = $data["msg"];
            if ($request->hasFile('files')) {
                if ((array_key_exists('files', $data)) && count($data["files"]) > 0) {
                    foreach ($data["files"] as $key => $value) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = storeFile("uploads/$tableName/", $value, $fileName);
                        $template->file = $path;
                    }
                }
            }
            $template->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Template Added", "status" => true])
                :
                redirect()->back()->with(["message" => "Template Created", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (!request()->user()->can("$this->tableName.view")) {
            abort(403, "unauthorized");
        }

        try {
            $totalCount = $_GET['perPage'] ?? 8;
            $backData['category_id'] = $id ?? null;
            $userIds = request()->user()->getAllChildUserIds();

            $templates = WaTemplateView::query();
            $templates = $templates->whereIn("user_id", $userIds);
            if ($id > 0) {
                $templates = $templates->where("sms_template_category_id", $id);
            }
            $templates = $templates->orderBy('datetime', 'desc');
            $backData['templates'] = $templates->paginate($totalCount)->withQueryString();
            $backData['getData'] = $_GET;

            $backData["can_add"] = request()->user()->can("$this->tableName.add");
            $backData["can_edit"] = request()->user()->can("$this->tableName.edit");
            $backData["can_delete"] = request()->user()->can("$this->tableName.delete");

            return (request()->header('Accept') == 'application/json')
                ? response()->json(['collection' => $backData])
                : Inertia::render('SMS2/Templates/Index', ['collection' => $backData]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['status' => false, 'message' => errorMessage($e->getCode())])
                : redirect()->back()->with(['type' => "failure", 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }
        $userIds = request()->user()->getAllChildUserIds();
        $categories = WaTemplateCategory::whereIn("user_id", $userIds)->get();
        $templates = WaTemplateView::whereIn("user_id", $userIds)->where('id', $id)->first();
        return response()->json(["categories" => $categories, "templates" => $templates]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $template)
    {
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }

        $request->validate(
            [
                'templateName' => 'required|max:45',
                'category_id' => 'required',
                'message' => 'bail|required_without:attachment',
                'attachment.*' => 'required_without:message|file|max:20000',
            ],
            [
                'msg.required_without' => "Please ensure that you provide either a message or a file upload, or both.",
                'files.required_without' => "Please ensure that you provide either a message or a file upload, or both."
            ],
            [
                "templateName" => 'Template Name',
                "msg" => 'Message',
                "category_id" => 'Category',
                "file.*" => "File",
                "file" => "Files",
                'attachment' => 'File',
                'message' => 'Message',
                'attachment.*' => 'File'
            ],
        );
        try {
            $template = WaTemplate::find($template);
            $userIds = request()->user()->getAllChildUserIds();
            if ($template) {
                $cate = WaTemplateCategory::whereIn("user_id", $userIds)->where(['id' => $template->sms_template_category_id])->first();
                if (empty($cate)) {
                    return (request()->header('Accept') == 'application/json') ?
                        response()->json(["message" => "Invalid Category Id", "status" => false])
                        : redirect()->back()->with(["message" => "Invalid Category Id", "type" => "failure"]);
                }


                $template->name = $request->templateName;
                $template->sms_template_category_id = $request->category_id;
                $template->body = $request->msg;

                if ($request->hasFile('files')) {
                    foreach ($request->files as $key => $value) {
                        if ($template->file && (Storage::disk('public')->exists($template->file))) {

                            Storage::disk('public')->delete($template->file);
                        }
                        $fileName = time() . "." . $value[0]->getClientOriginalExtension();
                        $tableName = $template->getTable();
                        $path = storeFile('uploads/'.$tableName, $value[0], $fileName);
                        $template->file = $path;
                    }
                }
                $template->save();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(["message" => "Template Updated SuccessFully", "status" => true])
                    : redirect()->route('sms2.templates.index')->with(["message" => "Template Updated SuccessFully", "type" => "success"]);
                // return redirect()->route('sms2.templates.index')->with(["message" => "Template Updated", "type" => "success"]);
            } else {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(["message" => "Invalid Template Id", "status" => false])
                    : redirect()->back()->with(["message" => "Invalid Template Id", "type" => "failure"]);
            }
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                : redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }



    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can("$this->tableName.delete")) {
            abort(403, "unauthorized");
        }
        try {
            $ids = explode(",", $id);
            $Templates = WaTemplate::whereIn('id', $ids)->with('category')->get();
            if (count($Templates) > 0) {
                $totalDeleted = 0;
                foreach ($Templates as $template) {
                    if ($template->category->user_id == Auth::id()) {
                        $totalDeleted++;
                        $template->delete();
                    }
                }
                if ($totalDeleted > 0) {
                    return (request()->header('Accept') == 'application/json') ?
                        response()->json(['message' => "Template deleted.", 'status' => true])
                        : redirect()->back()->with(["message" => "Template deleted.", "type" => "success"]);
                }
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Template NotFound.", 'status' => true])
                : redirect()->back()->with(["message" => "Record Not Found.", "type" => "failure"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
    public function fetchTemplateData($id = null, $perPage = 8)
    {
        $userIds = request()->user()->getAllChildUserIds();

        $categories = WaTemplateCategory::query();
        if (!request()->user()->isAdmin()) {
            $categories->whereIn("user_id",         $userIds);
        }
        $categories = $categories->paginate($perPage, "*", "categoryPage")->withQueryString();
        $templates = WaTemplateView::query();
        if (!request()->user()->isAdmin()) {
            $templates = $templates->whereIn("user_id",         $userIds);
        }
        if ($id) {
            $templates = $templates->where("sms_template_category_id", $id);
        }
        $templates = $templates->orderBy('datetime', 'desc');
        $templates = $templates->paginate($perPage)->withQueryString();

        return response()->json(["template" => $templates, 'categories' => $categories]);
    }
    public function addCategory(Request $request)
    {
        $request->validate([
            "name" => "required|max:45",
        ]);
        try {
            $category = new WaTemplateCategory();
            $category->name = $request->name;
            $category->user_id = Auth::id();
            $category->save();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Category Added", "type" => "success"])
                :
                redirect()->back()->with(["message" => "Category Added", "type" => "success"]);
        } catch (PDOException $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->errorInfo[1]), "type" => "failure"])
                :
                redirect()->back()->with(["message" => errorMessage($e->errorInfo[1]), "type" => "failure"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "type" => "failure"])
                :
                redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }
}
