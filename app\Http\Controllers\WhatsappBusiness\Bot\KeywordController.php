<?php

namespace App\Http\Controllers\WhatsappBusiness\Bot;

use Exception;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\WhatsappBusiness\bot\WaBaBot;
use App\Models\WhatsappBusiness\bot\WaBaBotKeyword;
use App\Models\WhatsappBusiness\bot\WaBaBotKeywordMsg;

class KeywordController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBaBotKeyword())->getTable();
    }
    public function isMyBot($bot)
    {
        $bot = WaBaBot::where(['user_id' => Auth::id(), 'id' => $bot])->first();
        if ($bot) {
            return $bot;
        } else {
            return false;
        }
    }
    public function index($bot)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);

        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }

        try {
            $search = request()->query('search', null);
            $perPage = request()->query('perPage', 10);
            $column = request()->query('sortColumn', 'id');
            $direction = request()->query('sortBy', 'desc');


            $models = [
                'keywordMessage' => WaBaBotKeywordMsg::class
            ];

            foreach ($models as $key => $model) {
                $table = (new $model())->getTable();
                $data["can_" . $key]["add"] = request()->user()->can($table . ".add");
                $data["can_" . $key]["edit"] = request()->user()->can($table . ".edit");
                $data["can_" . $key]["view"] = request()->user()->can($table . ".view");
                $data["can_" . $key]["delete"] = request()->user()->can($table . ".delete");
            }

            if (request()->header('Accept') == 'application/json') {
                $data["messages"] = WaBaBotKeyword::orderBy($column, $direction)
                    ->where('waba_bot_id', $bot)
                    ->when($search, function ($query, $search) {
                        return $query->whereAny(['keyword', 'id'], 'LIKE', "%{$search}%");
                    })
                    ->withCount('messages')
                    ->paginate($perPage)->withQueryString();
                return response()->json(['status' => true, "collection" => ['data' => $data, "can_add" => request()->user()->can($this->tableName . ".add"), "can_edit" => request()->user()->can($this->tableName . ".edit"), "can_delete" => request()->user()->can($this->tableName . ".delete")], 'message' => 'Messages fetched successfully']);
            } else {
                $data["messages"] = WaBaBotKeyword::orderBy($column, $direction)
                    ->where('waba_bot_id', $bot)
                    ->when($search, function ($query, $search) {
                        return $query->whereAny(['keyword', 'id'], 'LIKE', "%{$search}%");
                    })
                    ->withCount('messages')
                    ->paginate($perPage)->withQueryString();
                return Inertia::render('WhatsappBusiness/Bot/Keywords/Index', ["collection" => ['keywords' => $data, 'getData' => $_GET, 'bot' => $botData, "can_add" => request()->user()->can($this->tableName . ".add"), "can_edit" => request()->user()->can($this->tableName . ".edit"), "can_delete" => request()->user()->can($this->tableName . ".delete")]]);
            }
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function create($bot)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if ($botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        return Inertia::render('WhatsappBusiness/Bot/Steps/Add');
    }

    public function store(Request $request, $bot)
    {


        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        $request->validate([
            'keyword' => 'required|string',
            'enableAddMessage' => 'required'
        ]);
        try {
            $keyword = new WaBaBotKeyword();
            $keyword->keyword = $request->keyword;
            $keyword->waba_bot_id = $bot;
            $keyword->user_id = Auth::id();

            if ($request->enableAddMessage == 1) {
                $validator = Validator::make(
                    $request->all(),
                    [
                        'message' => 'bail|required_without:attachment',
                        'attachment.*' => 'required_without:message|file|max:20000',
                    ],
                    [],
                    ['attachment' => 'File', 'message' => 'Message', 'attachment.*' => 'File']
                );
                if ($validator->fails()) {
                    return (request()->header('Accept') == 'application/json')
                        ? response()->json(['message' => 'Unable to add Keyword.', 'errors' => $validator->errors(), 'status' => false])
                        : redirect()->back()->withErrors($validator->errors());
                }

                $keyword->save();
                $keywordMessage = new WaBaBotKeywordMsg();
                $keywordMessage->waba_bot_keyword_id = $keyword->id;
                $keywordMessage->body = $request->msg;

                if ($request->hasFile('attachment')) {
                    foreach ($request->attachment as $file) {
                        $fileName = time() . '-' . $file->getClientOriginalName();
                        $tableName = $keywordMessage->getTable();
                        $path = storeFile('uploads/' . $tableName, $file, $fileName);
                        $keywordMessage->file = $path;
                    }
                }
                $keywordMessage->save();

                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['status' => true, 'message' => 'Keyword added successfully']) :
                    redirect()->back()->with(["message" => 'Keyword added successfully', "type" => "success"]);
            }
            $keyword->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Keyword added successfully']) :
                redirect()->back()->with(["message" => 'Keyword added successfully', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    public function show($bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if ($botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        try {
            $keyword = WaBaBotKeyword::with('messages', 'bot')->find($id);
            if ($keyword->waba_bot_id != $bot) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Invalid Bot ID', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Invalid Bot ID', "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'data' => $keyword, 'message' => 'Keyword fetched successfully']) :
                Inertia::render('WhatsappBusiness/Bot/Keywords/Show', ['keyword' => $keyword]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function edit($bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if ($botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        try {
            $keyword = WaBaBotKeyword::find($id);
            if ($keyword->waba_bot_id != $bot) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Invalid Bot ID', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Invalid Bot ID', "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'data' => $keyword, 'message' => 'Keyword fetched successfully']) :
                Inertia::render('WhatsappBusiness/Bot/Keywords/Edit', ['keyword' => $keyword]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function update(Request $request, $bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }

        $request->validate([
            'keyword' => 'required|string',
        ]);
        try {
            $keyword = WaBaBotKeyword::find($id);

            if ($keyword->waba_bot_id != $bot) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Invalid Bot ID', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Invalid Bot ID', "type" => "failure"]);
            }
            $keyword->keyword = $request->keyword;
            $keyword->update();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Keyword updated successfully']) :
                redirect()->back()->with(["message" => 'Keyword updated successfully', "type" => "success"]);
        } catch (Exception $e) {

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function destroy($bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        try {
            $ids = explode(',', $id);
            // return $ids;
            $keyword = WaBaBotKeyword::whereIn('id', $ids)->where('waba_bot_id', $bot)->get();
            // return $keyword;
            if ($keyword->count() == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Keyword not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Keyword not found', "type" => "failure"]);
            }
            WaBaBotKeyword::whereIn('id', $ids)->where('waba_bot_id', $bot)->delete();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Keyword deleted successfully']) :
                redirect()->back()->with('success', 'Keyword deleted successfully');
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
