<?php

namespace App\Http\Controllers\SMS;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SMSController extends Controller
{
    public function index()
    {
   
        return Inertia::render('SMS/Index');
    }

    public function dashboard()
    {
   
        return Inertia::render('SMS/Dashboard');
    }
    public function campaign()
    {
   
        return Inertia::render('SMS/Campaign/Index');
    }
    public function addCampaign()
    {
   
        return Inertia::render('SMS/Campaign/AddCampaign');
    }
    public function messages()
    {
   
        return Inertia::render('SMS/Messages/Index');
    }
    public function bots()
    {
   
        return Inertia::render('SMS/Bots/Index');
    }

    public function listKeyword()
    {
   
        return Inertia::render('SMS/Bots/ListKeyword');
    }

    public function templates()
    {
        return Inertia::render('SMS/Templates/Index');
    }

    public function contacts()
    {
        return Inertia::render('SMS/Contacts');
    }
    public function gateways()
    {
        return Inertia::render('SMS/Gateways/Index');
    }
}
