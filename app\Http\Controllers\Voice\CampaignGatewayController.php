<?php

namespace App\Http\Controllers\Voice;

use App\Http\Controllers\Controller;
use App\Models\Voice\CampaignGateway;
use Exception;
use Illuminate\Http\Request;

class CampaignGatewayController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            // $userIds = request()->user()->getAllChildUserIds();
            $ids = explode(",", $id);

            $campaignGateways = CampaignGateway::whereIn('id', $ids)->get();
          if (!empty($campaignGateways)) {
                CampaignGateway::whereIn('id', $ids)->delete();
                $message = "Campaign Gateway removed.";
                $status = true;
            } else {
                $message = "Gateway Not Found.";
                $status = false;
            }

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => $status])
                : redirect()->back()->with(["message" => $message, "type" => $status ? "success" : "failure"]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
