import { router } from "@inertiajs/react";
import { BsFiletypeExe } from "react-icons/bs";

export function changeFlag(table, column, id, flag, setLoading = null) {
    if (setLoading) setLoading(true);
    router.post(route('helper.changeFlag'), 
        { 'table': table, 'column': column, 'id': id, 'flag': flag },
        {
            onSuccess: () => {
                if (setLoading) setLoading(false);
            },
            onError: () => {
                if (setLoading) setLoading(false);
            }
        }
    );
}
export function deleteSingle(table, id, message) {
    router.post(route('helper.singleDelete'), { 'table': table, 'id': id, 'message': message });

}
export const fetchJson = (routeName, params = {}, isUrl = false) => fetch(isUrl ? routeName : route(routeName, params), {
    headers: {
        'Accept': "application/json"
    }
}).then(res => res.json());
export const validImageExtensions = ["jpeg", "jpg", "webp", "gif", "png"];


export function convertDateFormat(dateString, type = 'full', separator = "/", format = 'DD/MM/YYYY HH:mm') {
    const inputDate = new Date(dateString);
    const pad = (value) => String(value).padStart(2, '0');

    const year = inputDate.getFullYear();
    const month = pad(inputDate.getMonth() + 1);
    const day = pad(inputDate.getDate());
    const hours = pad(inputDate.getHours());
    const minutes = pad(inputDate.getMinutes());

    if (type === 'date') {
        return `${day}${separator}${month}${separator}${year}`;
    } else if (type == 'time') {
        return `${hours}:${minutes}`;
    } else {
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes);
    }
}

export function isImage(fileName) {
    const extension = fileName.split(".").pop();
    return validImageExtensions.includes(extension);
}

export const backHelper = () => window.history.back();

export function isDateGreaterThanToday(inputDate) {
    const currentDate = new Date();
    const inputDateObj = new Date(inputDate);

    return inputDateObj > currentDate;
}

export function isScheduled(dateTimeString) {
    const inputDateTime = new Date(dateTimeString);
    // Get the current date and time.
    const now = new Date();
    // Compare the input date with the current date.
    return inputDateTime > now;
}

export function showAsset(name = '', basePath = "/storage/") {
    return document.location.origin + basePath + name;
}


import { FaFileAudio, FaFileCsv, FaFileExcel, FaFilePdf, FaFilePowerpoint, FaFileVideo, FaFileWord, FaFileZipper } from "react-icons/fa6";
import { LuFileJson2 } from "react-icons/lu";
import { RiFileTextFill } from "react-icons/ri";
export const fileIcons =
{
    'pdf': <FaFilePdf />,
    'mp3': <FaFileAudio />,
    'csv': <FaFileCsv />,
    'mp4': <FaFileVideo />,
    'ppt': <FaFilePowerpoint />,
    'pptx': <FaFilePowerpoint />,
    'doc': <FaFileWord />,
    'docs': <FaFileWord />,
    'text': <RiFileTextFill />,
    'txt': <RiFileTextFill />,
    'zip': <FaFileZipper />,
    'xls': <FaFileExcel />,
    'xlsx': <FaFileExcel />,
    'json': <LuFileJson2 />,
    'exe': <BsFiletypeExe />
};

export function extractVariables(text) {
    const matches = text.match(/\[\[(.*?)\]\]/g) || [];
    const placeholders = {};
    matches.forEach((match) => {
        const key = match.slice(2, -2); // Remove [[ and ]]
        placeholders[key] = ""; // Set the key with an empty string as its value
    });
    return placeholders;
}

export function removePrefix(text) {
    // Find the index of the first hyphen
    const hyphenIndex = text.indexOf('-');

    // If no hyphen is found, remove text after the last slash
    if (hyphenIndex === -1) {
        const lastSlashIndex = text.lastIndexOf('/');
        return lastSlashIndex !== -1 ? text.substring(lastSlashIndex + 1) : text;
    }

    // Remove text before the hyphen
    return text.substring(hyphenIndex + 1);
}

export function getIconForFile(fileName, need = 'icon') {
    // Split the file name by the dot (.) to get the name and extension
    const parts = fileName.split('.');
    // const [name, extension] = fileName.split('.');
    const name = parts[0];
    const extension = parts[parts.length - 1]

    switch (need) {
        case 'icon':
            // Implement logic to get the icon based on the extension
            return fileIcons[extension] || extension;

        case 'ext':
            return extension;

        case 'name':
            return name;

        case 'fullName':
            return fileName;

        default:
            return fileName;
    }
}

// check is date is today or not
export function isDateToday(date) {
    const today = new Date();
    return (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    );
}



export function textFormatting(input) {
    // Replace bold (*text*)
    const boldPattern = /\*(.*?)\*/g; // Single asterisk for bold
    input = input.replace(boldPattern, "<strong>$1</strong>");
    // Replace italic (_text_)
    const italicPattern = /_(.*?)_/g; // Escape _ to match underscores literally
    input = input.replace(italicPattern, "<span className='italic' >$1</span>");


    // Replace strikethrough (~text~)
    const strikeThroughPattern = /~(.*?)~/g; // Single tilde for strikethrough
    input = input.replace(strikeThroughPattern, "<del>$1</del>");
    return input;
}



export const getColumnRouteForSorting = (routeName, column, sortBy = 'desc', params = {}) => {
    return route(routeName, { column: column, ...params, sort: sortBy });
}


