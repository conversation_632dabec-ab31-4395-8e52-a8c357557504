<?php

namespace App\Http\Controllers\WhatsappBusiness;

use App\Helpers\WhatsappBusinessServices;
use App\Http\Controllers\Controller;
use App\Http\Requests\TemplateFormateRequest;
use App\Models\WaTemplate;
use App\Models\WaTemplateCategory;
use App\Models\WhatsappBusiness\WaBaGateway;
use App\Models\WhatsappBusiness\WaBaTemplate;
use App\Models\WhatsappBusiness\WaBaTemplateCategory;
use App\Models\WhatsappBusiness\WaBaTemplateView;
use CURLFile;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class TemplatesController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaTemplate())->getTable();
    }
    /**
     * Display a listing of the resource.
     */

    public function index(Request $request)
    {
        if (!request()->user()->can("$this->tableName.view")) {
            abort(403, "unauthorized");
        }

        try {
            $totalCount = $request->query('total', 10);
            $search = $request->query('search', null);
            $status = $request->query("status", null);
            $gateway = $request->query("gateway", null);
            $data['category_id'] = $request->query('category', null);
            $userIds = $request->user()->getAllChildUserIds();

            // Template Data fetch Start
            $templates = WaBaTemplateView::query();
            $templates = $templates->whereIn("user_id", $userIds);

            if ($status) {
                $templates->whereIn("user_id", $userIds)->where("metaStatus", strtoupper($status));
            }
            if ($gateway) {
                $templates->where("waba_gateway_id", $gateway);
            }

            $templates = $templates->when($search, function ($query, $search) {
                return $query->where('name', 'LIKE', "%" . $search . "%")
                    ->orWhere('id', 'LIKE', "%" . $search . "%");
            });

            $templates = $templates->orderBy('datetime', 'desc');

            $data['templates'] = $templates->with('gateway')->paginate($totalCount)->withQueryString();
            // Template Data fetch End

            // $data["gateways"] = WaBaGateway::whereIn('user_id', $userIds)
            //     ->get();

            // Category Data fetch Start
            $data["categories"] = WaBaTemplateCategory::query();
            $data["categories"] = $data["categories"]->whereIn("user_id", $userIds);
            $data["categories"] = $data["categories"]->withCount('template')->get();
            // Category Data fetch End

            $models = [
                'category' => WaBaTemplateCategory::class,
                'template' => WaBaTemplateView::class,
            ];

            foreach ($models as $key => $model) {
                $table = (new $model())->getTable();
                $data["can_" . $key]["add"] = request()->user()->can($table . ".add");
                $data["can_" . $key]["edit"] = request()->user()->can($table . ".edit");
                $data["can_" . $key]["view"] = request()->user()->can($table . ".view");
                $data["can_" . $key]["delete"] = request()->user()->can($table . ".delete");
            }

            $getData = $_GET;

            return (request()->header('Accept') == 'application/json')
                ? response()->json(["collection" => $data, "getData" => $getData])
                : Inertia::render('WhatsappBusiness/Templates/Index', ["collection" => $data, "getData" => $getData, "category_id" => 0]);
        } catch (Exception $e) {
            return redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('WhatsappBusiness/Templates/AddTemplate');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TemplateFormateRequest $request)
    {

        try {
            if (($request->headerFormat != "" && $request->headerFormat != "TEXT") && $request->headerText == null && $request->headerFile == null && $request->url == null) {
                return redirect()->back()->with(["message" => "Please provide a valid header file.", "type" => "failure"]);
            }
            $gateway = WaBaGateway::find($request->gateway);
            if (!$gateway) {
                return redirect()->back()->with(["message" => "Gateway Not Found!", "type" => "failure"]);
            }
            $whatsappService = new WhatsappBusinessServices($gateway->phone_id, $gateway->whatsAppBusinessAccountID, $gateway->permanentAccessToken);
            $tmp_template_json = $request->template_json;
            $template = new WaBaTemplate();
            if (($request->headerFormat != "" && $request->headerFormat != "TEXT") && $request->headerText == null && $request->headerFile) {
                $headerFileName = time() . "-" . $request->headerFile->getClientOriginalName();
                $headerFilePath = Storage::disk('public')->putFileAs(
                    "uploads/waBaTemplate",
                    $request->headerFile,
                    $headerFileName
                );
                $template->url = $headerFilePath ?? null;
                $newMediaUploadResult = $whatsappService->metaTemplateMediaUploadCreate($headerFileName,  $request->headerFile->getMimeType(), $request->headerFile);
                if (!isset($newMediaUploadResult["h"])) {
                    return redirect()->back()->with(["message" => errorMessage(), "type" => "failure"]);
                }

                $tmp_template_json["components"] = collect($tmp_template_json["components"])->map(function ($component) use ($newMediaUploadResult, $request) {
                    if ($component["type"] == "HEADER") {
                        if (isset($component["example"])) {
                            $component["example"]["header_handle"] = $request->variablePreference == "name"
                                ? [["param_name" => "header_" . strtolower($request->headerFormat), "example" => $newMediaUploadResult["h"]]]
                                : [$newMediaUploadResult["h"]];
                        } else {
                            $component["example"] = ["header_handle" => [$newMediaUploadResult["h"]]];
                        }
                    }
                    return $component;
                })->toArray();
            }

            $templateResult = $whatsappService->templateCreate($tmp_template_json);

            if (array_key_exists("error", $templateResult) || $templateResult["status"] == false) {
                return redirect()->back()->with(["message" => $templateResult["error"]["error_user_msg"] ?? $templateResult["error"]["message"], "type" => "failure"]);
            }
            $template->name = $request->templateName;
            $template->waba_template_category_id = $request->category_id;
            $template->waba_gateway_id = $request->gateway;
            $template->templateJson = json_encode($tmp_template_json);
            $template->metaTemplateId =  $templateResult["id"];
            $template->metaCategory = $tmp_template_json["category"];
            $template->language = $tmp_template_json["language"];
            $template->variableType = $request->variablePreference;
            $template->status = $templateResult["status"];
            $template->save();
            return redirect()->route('whatsappB.templates.index')->with(["message" => "Template Added", "type" => "success"]);
        } catch (Exception $e) {
            return redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */

    public function show(string $categoryName)
    {

        $totalCount = $_GET['perPage'] ?? 8;
        $status = request()->query('status', null);
        $userIds = request()->user()->getAllChildUserIds();

        $template = WaBaTemplateView::query();
        $template = $template->whereIn("user_id", $userIds);
        $template = $template->where('metaCategory', $categoryName);

        if ($status) {
            $template->where("metaStatus", strtoupper($status));
        }


        $template = $template->orderBy('datetime', 'desc');
        $data["templates"] = $template->with('gateway')->paginate($totalCount, "*", "templatePage")->withQueryString();
        $models = [
            // 'category' => WaBaTemplateCategory::class,
            'template' => WaBaTemplateView::class,
        ];

        foreach ($models as $key => $model) {
            $table = (new $model())->getTable();
            $data["can_" . $key]["add"] = request()->user()->can($table . ".add");
            $data["can_" . $key]["edit"] = request()->user()->can($table . ".edit");
            $data["can_" . $key]["view"] = request()->user()->can($table . ".view");
            $data["can_" . $key]["delete"] = request()->user()->can($table . ".delete");
        }

        $data["gateways"] = WaBaGateway::whereIn('user_id', $userIds)
            ->get();

        $getData = $_GET;
        return (request()->header('Accept') == 'application/json')
            ? response()->json(["collection" => $data, "getData" => $getData, "selectedCategoryName" => $categoryName])
            : Inertia::render('WhatsappBusiness/Templates/Index', ["collection" => $data, "getData" => $getData, "selectedCategoryName" => $categoryName]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            $data["template"] = WaBaTemplate::with('gateway')->find($id);
            if ($data["template"] == null) {
                return redirect()->back()->with(["message" => "Template not Found", "type" => "failure"]);
            }
            $whatsappService = new WhatsappBusinessServices($data["template"]->gateway->phone_id, $data["template"]->gateway->whatsAppBusinessAccountID, $data["template"]->gateway->permanentAccessToken);
            $data["metaTemplateJson"] = $whatsappService->getMetaTemplateDetails($data["template"]->name);
            if (isset($data["metaTemplateJson"]["data"]) && count($data["metaTemplateJson"]["data"]) == 0) {
                return redirect()->back()->with(["message" => "Template not Found", "type" => "failure"]);
            }
            return Inertia::render('WhatsappBusiness/Templates/Edit', ["collection" => $data]);
        } catch (Exception $e) {
            return redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TemplateFormateRequest $request)
    {
        try {
            $template = WaBaTemplate::with('gateway')->find($request->templateId);
            if (!$template) {
                return redirect()->back()->with(["message" => "Template Not Found!", "type" => "failure"]);
            }

            if (($request->headerFormat != "" && $request->headerFormat != "TEXT") && $request->headerText == null && $request->headerFile == null && $request->url == null) {
                return redirect()->back()->with(["message" => "Please provide a valid header file.", "type" => "failure"]);
            }

            $tmp_template_json = $request->template_json;
            if ($request->headerFormat && $request->headerFormat != "TEXT" && !$request->headerText && $request->headerFile) {
                // Delete old file if exists
                if ($template->url) {
                    $oldFilePath = str_replace(request()->getSchemeAndHttpHost() . "/storage/", "", $template->url);
                    if (Storage::disk('public')->exists($oldFilePath)) {
                        Storage::disk('public')->delete($oldFilePath);
                    }
                }

                // Store new file
                $headerFileName = time() . "-" . $request->headerFile->getClientOriginalName();
                $headerFilePath = Storage::disk('public')->putFileAs(
                    "uploads/waBaTemplate",
                    $request->headerFile,
                    $headerFileName
                );

                $headerUrl = request()->getSchemeAndHttpHost() . "/storage/" . $headerFilePath;
                $template->url = $headerUrl;

                // Update template JSON components
                $tmp_template_json["components"] = collect($tmp_template_json["components"])->map(function ($component) use ($headerUrl, $request) {
                    if ($component["type"] == "HEADER") {
                        $example = $request->variablePreference == "name"
                            ? [["param_name" => "header_" . strtolower($request->headerFormat), "example" => $headerUrl]]
                            : [$headerUrl];
                        $component["example"]["header_handle"] = $example;
                    }
                    return $component;
                })->toArray();
            }

            $whatsappService = new WhatsappBusinessServices($template->gateway->phone_id, $template->gateway->whatsAppBusinessAccountID, $template->gateway->permanentAccessToken);
            $templateResult = $whatsappService->updateTemplate($request->metaTemplateId, $tmp_template_json);
            if (isset($templateResult["success"])) {
                $template->waba_template_category_id = $request->category_id;
                $template->waba_gateway_id = $request->gateway;
                $template->language = $tmp_template_json["language"];
                $template->templateJson = json_encode($tmp_template_json);
                $template->metaTemplateId =  $templateResult["id"];
                $template->metaCategory = $templateResult["category"];
                $template->url = $request->url;
                $template->variableType = $request->variablePreference;
                $template->save();
                return redirect()->route('whatsappB.templates.index')->with(["message" => "Template Updated", "type" => "success"]);
            } else {
                return redirect()->back()->with(["message" => $templateResult["error"]["error_user_msg"], "type" => "failure"]);
            }
        } catch (Exception $e) {
            return redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $template = WaBaTemplate::with('gateway')->find($id);
            if (!$template) {
                return redirect()->back()->with(["message" => "Template not found", "type" => "failure"]);
            }
            $whatsappService = new WhatsappBusinessServices($template->gateway->phone_id, $template->gateway->whatsAppBusinessAccountID, $template->gateway->permanentAccessToken);
            $result = $whatsappService->deleteTemplate($template->name, $template->metaTemplateId);

            if (isset($result["error"]) || (isset($result["status"]) && !$result["status"])) {
                if ($result["code"] == 100) {
                    $template->delete();
                    return redirect()->back()->with(["message" => "Template Deleted", "type" => "success"]);
                }
                return redirect()->back()->with(["message" => $result["error"]["error_user_msg"], "type" => "failure"]);
            }

            $template->delete();
            return redirect()->back()->with(["message" => "Template Deleted", "type" => "success"]);
        } catch (Exception $e) {
            return redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
    public function fetchTemplateData($catName = null, $perPage = 8)
    {
        $totalCount = $perPage ?? 8;
        $categoryName = $catName ?? null;
        $userIds = request()->user()->getAllChildUserIds();
        $templates = WaBaTemplateView::query();
        $templates = $templates->where("user_id", $userIds);
        if ($categoryName) {
            $templates = $templates->where("metaCategory", $categoryName);
        }
        $templates = $templates->orderBy('datetime', 'desc');
        $templates = $templates->paginate($totalCount)->withQueryString();
        $getData = $_GET;
        return response()->json(["template" => $templates]);
    }
    public function addCategory(Request $request)
    {
        $validated = $request->validate([
            "name" => "required|max:45",
        ]);
        try {
            $category = new WaBaTemplateCategory();
            $category->name = $request->name;
            $category->user_id = Auth::id();
            $category->save();
            return redirect()->back()->with(["message" => "Category Added", "type" => "success"]);
        } catch (Exception $e) {
            return redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }
    public function updateCategoryData(Request $request)
    {
        $validated = $request->validate([
            "name" => "required|max:45",
        ]);
        try {
            $category = WaTemplateCategory::find($request->id);
            $category->name = $request->name;
            // $category->user_id = Auth::id();
            $category->save();
            return redirect()->back()->with(["message" => "Category Updated", "type" => "success"]);
        } catch (Exception $e) {
            return redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }

    public function editCategoryData($id)
    {
        $categories = WaTemplateCategory::where("user_id", Auth::id())->where("id", $id)->first();
        return response()->json(["categories" => $categories]);
    }

    function sendTemplate(Request $request)
    {
        $validate = $request->validate(['*' => 'required']);
        try {
            $template = WaBaTemplate::find($request->template);
            if ($template) {
                $str = $template->msgBody;

                // Regular expression to match [[key]]
                $str = preg_replace_callback('/\[\[(.*?)\]\]/', function ($matches) use ($request) {
                    $key = $matches[1];
                    return isset($request[$key]) ? $request[$key] : $matches[0]; // Replace if exists, else leave the original
                }, $str);

                if (count($request->attachment) > 0) {
                    // $imageIds = [];
                    foreach ($request->attachment as $key => $value) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = Storage::disk('public')->putFileAs(
                            'uploads/send/template',
                            $value,
                            $fileName,
                        );
                     
                    }
                }
            }
          
        } catch (Exception $e) {
           }
    }
    public function fetchAllTemplatesMeta($gatewayId = null, $nextUrl = null)
    {
        try {
            $gateway = WaBaGateway::find($gatewayId);
            if (!$gateway) {
                return response()->json(["status" => false, "message" => "Gateway not found"]);
            }

            $whatsappBusinessService = new WhatsappBusinessServices($gateway->phone_id, $gateway->whatsAppBusinessAccountID, $gateway->permanentAccessToken, "v22.0");
            $result = collect($whatsappBusinessService->fetchTemplates($nextUrl));
            if ($result->has('error')) {
                session()->flash("type", "failure");
                session()->flash("message", $result["error"]["error_user_msg"] ?? $result["error"]["message"]);
                return ["status" => true, "message" => "Session Expired"];
            }

            $bulkData = collect($result->get('data'))->map(function ($data) use ($gatewayId) {
                return [
                    "name" => $data["name"],
                    "templateJson" => json_encode($data),
                    "metaTemplateId" => $data["id"],
                    "language" => $data["language"],
                    "metaCategory" => $data["category"],
                    "status" => $data["status"],
                    "waba_gateway_id" => $gatewayId
                ];
            })->toArray();

            $upsertResult = WaBaTemplate::upsert(
                $bulkData,
                ["metaTemplateId"],
                ['name', "templateJson", 'metaTemplateId', "language", "metaCategory", "status", "waba_gateway_id"]
            );

            if ($result->has('paging') && isset($result->get('paging')["next"])) {
                $this->fetchAllTemplatesMeta($result->get('paging')["next"]);
            }
            session()->flash("type", "success");
            session()->flash("message", "Synchronized successfull");
            return ["status" => true, "message" => "Synchronization successfull"];
        } catch (Exception $e) {
            session()->flash("type", "failure");
            session()->flash("message", errorMessage($e->getCode()));
            return ["status" => false, "message" => errorMessage($e->getCode())];
        }
    }
}
