<?php

namespace App\Http\Controllers\WhatsappBusiness;

use App\Helpers\WhatsappBusinessServices;
use Exception;
use PDOException;

use Inertia\Inertia;
use App\Models\WaMsgs;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use App\Models\WhatsappBusiness\WaBaGateway;

class GatewayController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBaGateway())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }
        try {
            $totalCount = $_GET['perPage'] ?? 10;
            $column = $_GET['column'] ?? 'id';
            $sortBy = $_GET['sort'] ?? 'desc';
            $search = $_GET['search'] ?? null;
            $data['getData'] = $_GET;

            $getUser = request()->query('userID');

            $data["gateways"] = WaBaGateway::query();
            $data["gateways"] = $data["gateways"]->orderBy($column, $sortBy);

            if ($getUser) {
                $user = User::find($getUser);
                $userIds = request()->user()->getAllChildUserIds($getUser);
                $data["gateways"] = $data["gateways"]->orderBy($column, $sortBy);
                if (request()->user()->isAdmin() || in_array($user->id, $userIds->toArray())) {
                    $data["gateways"] = $data["gateways"]->whereIn('user_id', $userIds);
                } else {
                    return (request()->header('Accept') == 'application/json')
                        ? response()->json(['message' => 'Invalid request.', 'status' => true])
                        : redirect()->back()->with(["message" => 'Invalid request.', "type" => "failure"]);
                }
            } else {
                $userIds = request()->user()->getAllChildUserIds();
                $data["gateways"] = $data["gateways"]->whereIn("user_id", $userIds);
            }

            $data["gateways"] = $data["gateways"]->with('user');

            $data["gateways"] = $data["gateways"]->when($search, function ($query, $search) {
                return $query->whereAny(['name', 'id'], 'like', "%$search%");
            })->paginate($totalCount)->withQueryString();

            $data["can_add"] = request()->user()->can($this->tableName . ".add");
            $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
            $data["can_delete"] = request()->user()->can($this->tableName . ".delete");

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Gateway List.", 'status' => true, 'collection' => $data])
                : Inertia::render('WhatsappBusiness/Gateways/Index', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // try {

        // } catch (Exception $e) {
        //     return (request()->header('Accept') == 'application/json') ?
        //         response()->json(["message" => errorMessage($e->getCode()), "status" => false])
        //         : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        // }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required",
            "permanentAccessToken" => "required_if:getMetaChannel,false",
            "whatsAppBusinessAccountID" => "required_if:getMetaChannel,false",
            "phoneID" => "required_if:getMetaChannel,false",
            "phoneNumber" => "required_if:getMetaChannel,false",
            "getMetaChannel" => "required",
        ], [
            "*.required_if" => ":attribute is Required.",
        ], [
            "name" => "Name",
            "permanentAccessToken" => "Permanent Access Token",
            "whatsAppBusinessAccountID" => "WhatsApp Business Account ID",
            "phoneID" => "Phone ID",
            "phoneNumber" => "Phone Number",
            "getMetaChannel" => "Get Meta Channel",
        ]);
        try {

            $gateway = new WaBaGateway();
            $gateway->name = $request->name;
            $gateway->permanentAccessToken = $request->permanentAccessToken;
            $gateway->whatsAppBusinessAccountID = $request->whatsAppBusinessAccountID;
            $gateway->phone_id = $request->phoneID;
            $gateway->phoneNumber = $request->phoneNumber;
            $gateway->user_id = Auth::id();
            $gateway->save();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "GateWay Created.", "status" => true])
                : redirect()->back()->with(["message" => "GateWay Created.", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // try {

        // } catch (Exception $e) {
        //     return (request()->header('Accept') == 'application/json') ?
        //         response()->json(["message" => errorMessage($e->getCode()), "status" => false])
        //         : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        // }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // try {

        // } catch (Exception $e) {
        //     return (request()->header('Accept') == 'application/json') ?
        //         response()->json(["message" => errorMessage($e->getCode()), "status" => false])
        //         : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        // }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required",
            "permanentAccessToken" => "required_if:getMetaChannel,false",
            "whatsAppBusinessAccountID" => "required_if:getMetaChannel,false",
            "phoneID" => "required_if:getMetaChannel,false",
            "phoneNumber" => "required_if:getMetaChannel,false",
            "getMetaChannel" => "required",
        ], [
            "*.required_if" => ":attribute is Required.",
        ], [
            "name" => "Name",
            "permanentAccessToken" => "Permanent Access Token",
            "whatsAppBusinessAccountID" => "WhatsApp Business Account ID",
            "phoneID" => "Phone ID",
            "phoneNumber" => "Phone Number",
            "getMetaChannel" => "Get Meta Channel",
        ]);

        try {
            $userIds = $request->user()->getAllChildUserIds();
            $gateway = WaBaGateway::whereIn("user_id", $userIds)->where(['id' => $id])->first();
            if ($gateway) {
                $gateway->name = $request->name;
                $gateway->permanentAccessToken = $request->permanentAccessToken;
                $gateway->whatsAppBusinessAccountID = $request->whatsAppBusinessAccountID;
                $gateway->phone_id = $request->phoneID;
                $gateway->phoneNumber = $request->phoneNumber;
                $gateway->user_id = Auth::id();
                $gateway->update();
            } else {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(["message" => "GateWay Not Found.", "status" => false])
                    : redirect()->back()->with(["message" => "GateWay Not Found.", "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "GateWay Created.", "status" => true])
                : redirect()->back()->with(["message" => "GateWay Created.", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }
        try {
            $userIds = request()->user()->getAllChildUserIds();
            $ids = explode(",", $id);

            $gateways = WaBaGateway::whereIn('id', $ids)->whereIn('user_id', $userIds->toArray())->get();

            if ($gateways->isNotEmpty()) {
                WaBaGateway::whereIn('id', $ids)->whereIn('user_id', $userIds->toArray())->delete();
                $message = "Gateway deleted.";
                $status = true;
            } else {
                $message = "Gateway Not Found.";
                $status = false;
            }

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => $status])
                : redirect()->back()->with(["message" => $message, "type" => $status ? "success" : "failure"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    public function getChat($channel)
    {
        if (!request()->user()->can($this->tableName . '.view')) {
            abort(403, "unauthorized");
        }

        try {
            $savedChat =
                WaMsgs::where('wa_gateway_id', $channel)->with('user')->get();
            return response()->json(['chats' => $savedChat, 'count' => $savedChat->count()]);
        } catch (PDOException $e) {
            return redirect()->back()->with(["message" => $e->errorInfo[2], "type" => "failure"]);
        }
        // return "{$channel}";
    }


    public function updateDefault(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }

        try {
            $message = "Gateway Updated.";
            $campaign = WaBaGateway::find($request->id);
            if ($campaign) {
                WaBaGateway::where(['user_id' => Auth::id(), 'isDefault' => 1])->update(['isDefault' => 0]);
                $campaign->isDefault = $campaign->isDefault == 1 ? 0 : 1;
                $campaign->update();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => $message, 'status' => true])
                    : redirect()->back()->with(["message" => $message, "type" => "success"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Record Not Found.", 'status' => false])
                : redirect()->back()->with(["message" => "Record Not Found.", "type" => "failure"]);
        } catch (PDOException $e) {
            session()->flash('message', errorMessage($e->errorInfo[1]));
            session()->flash("type", "failure");
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->errorInfo[1]), 'status' => false])
                : redirect()->back();
        }
    }
    public function handleEmbeddedSignupCallback(Request $request)
    {
        try {
            ["facebookAuth" => $facebookAuth, "whatsappBusiness" => $whatsappBusiness] = $request->all();
            $whatsappBusinessService = new WhatsappBusinessServices();
            $response =  $whatsappBusinessService->getAccessToken($facebookAuth['code']);
            if (!isset($response['access_token'])) {
                throw new Exception("Failed to get access token.");
            }
            $gateway = new WaBaGateway();
            $gateway->name = $request->gatewayName ?? "New Gateway";
            $gateway->permanentAccessToken = $response['access_token'];
            $gateway->whatsAppBusinessAccountID = $whatsappBusiness['waba_id'];
            $gateway->phone_id = $whatsappBusiness['phone_number_id'];
            $gateway->metaBusinessID = $whatsappBusiness['business_id'];
            $gateway->phoneNumber = null;
            $gateway->user_id = Auth::id();
            $gateway->save();
            return response()->json(["message" => "Gateway Created", "type" => "success"]);
        } catch (Exception $e) {
            return redirect()->route('whatsappB.gateway.index')->with([
                'message' => errorMessage($e->getCode()),
                'type' => 'failure'
            ]);
        }
    }
}
