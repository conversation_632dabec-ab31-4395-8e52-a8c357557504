<?php

namespace App\Http\Controllers\WhatsappBusiness;

use App\Http\Controllers\Controller;
use App\Models\WaBot;
use App\Models\WaManualSending;
use App\Models\WaMsgs;
use Carbon\Carbon;
use Exception;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ChatsController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaManualSending())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        return Inertia::render('WhatsappBusiness/Chats/Index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(
            [
                'message' => 'bail|required_without:attachment',
                'attachment.*' => 'required_without:message|file|max:20000',
                "sendTo" => "required",
            ],
            [],
            ['attachment' => 'File', 'message' => 'Message', 'attachment.*' => 'File']
        );

        try {
            $message = new WaManualSending();
            $message->mobile = explode('@', $request->sendTo)[0];
            $message->messageDateTime = $request->isScheduled ? new \DateTime($request->ScheduleTime) : Carbon::now();
            $message->user_id = Auth::id();

            $message->wa_gateway_id = $request->gateway;
            $message->body = $request->message;
            if ($request->hasFile('attachment')) {

                foreach ($request->attachment as $key => $value) {
                    if (isset($value)) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = storeFile('uploads/' . $this->tableName, $value, $fileName);
                        $message->file = $path ?? "";
                    }
                }
            }
            $message->save();
            return redirect()->back()->with(["chatMsg" => "Chat Message Added", "type" => "success"]);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'msg' => errorMessage()]);
        }
    }

    /*
    array:6 [▼
          "message" => "sdfsfsdfsdf"
          "attachment" => []
          "isScheduled" => false
          "ScheduleTime" => "2024-11-14T12:39:38.646Z"
          "template_id" => null
          "sendTo" => "<EMAIL>"
        ]
    */

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // $data = WaMsgs::whereLike('remoteJid', $id)->paginate(1);
        try {

            $data = WaMsgs::whereLike('remoteJid', "%" . $id . "%")->orderBy('messageDateTime', 'desc')->paginate(10);
            return response()->json(['data' => $data, 'id' => $id,'status'=>true]);
        } catch (Exception $e) {
            return response()->json(['data' => null, 'id' => null,'status'=>false]);
         
        }

        // $groupData = $data->groupBy(function ($item) {
        //     return Carbon::parse($item->messageDateTime)->format('Y-m-d'); // Format as YYYY-MM-DD
        // });
        // return response()->json(['data' => $data, 'id' => $id, 'groupData' => $groupData]);

    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
