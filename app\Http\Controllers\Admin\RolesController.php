<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Dashboard;
use Exception;
use Inertia\Inertia;
use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;


class RolesController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new Role())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }
        try {
            $search = request()->query('search', null);
            $perPage = request()->query('perPage', 10);
            $column = request()->query('sortColumn', 'id');
            $direction = request()->query('sortBy', 'desc');

            $data['roles'] = Role::orderBy($column, $direction)
                ->when($search, function ($query, $search) {
                    return $query->where('name', 'LIKE', "%" . $search . "%")
                        ->orWhere('id', 'LIKE', "%" . $search . "%");
                })
                ->paginate($perPage)->withQueryString();
            $data['getData'] = $_GET;

            $data["can_add"] = request()->user()->can($this->tableName . ".add");
            $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
            $data["can_delete"] = request()->user()->can($this->tableName . ".delete");
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['collection' => $data, 'status' => true]) :
                Inertia::render('Admin/Roles/Index', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:Spatie\Permission\Models\Role,name',
                // 'description' => 'nullable|string|max:1000',
            ]);
            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator);
            }
            // app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(1);
            Role::create(['name' => $request->name]);
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Role created successfully.', 'status' => true]) :
                redirect()->back()->with(["message" => 'Role created successfully.', "type" => "success"]);


            // return redirect()->route('admin.roles.index')->with('success', ');
        } catch (Exception $e) {

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
            // return redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (!request()->user()->can($this->tableName . '.view')) {
            abort(403, "unauthorized");
        }
        try {
            $data['role'] = Role::findOrFail($id);
            $data['permissions'] = $data['role']->permissions()->pluck('id');
            return response()->json(['status' => true, 'data' => $data]);
        } catch (Exception $e) {
            return
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }
        try {
            $data['role'] = Role::find($id);
            if (!$data['role']) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Role not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Role not found', "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'collection' => $data, 'message' => 'Role fetched successfully']) :
                Inertia::render('Admin/Roles/Edit', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:' . Role::class . ',name',
        ]);
        try {
            $role = Role::find($id);
            if (!$role) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Role not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Role not found', "type" => "failure"]);
            }

            $role->name = $request->name;
            // $role->description = $request->description;
            $role->update();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Role updated successfully']) :
                redirect()->back()->with(["message" => 'Role updated successfully', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }

        try {
            $ids = explode(',', $id);
            $keyword = Role::whereIn('id', $ids)->get();
            if ($keyword->count() == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Role not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Role not found', "type" => "failure"]);
            }
            Role::whereIn('id', $ids)->delete();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Role deleted successfully']) :
                redirect()->back()->with('success', 'Role deleted successfully');
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    public function assignPermission(Request $request, $id)
    {
        try {
            $role = Role::findOrFail($id);
            $permissions = Permission::whereIn('id', $request->permissions)->get();
            $role->syncPermissions($permissions);
            $this->logoutByRole($role->name);
            Artisan::call('optimize:clear');
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Role Updated successfully']) :
                redirect()->back()
                ->with(['message' => 'Role Updated successfully', 'type' => 'success']);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function logoutByRole($role)
    {
        try {
            $users = User::role($role)->get();
            foreach ($users as $u) {
                DB::table('sessions')
                    ->where('user_id', $u->id)
                    ->delete();
            }
            return response()->json(['status' => true, 'message' => 'Logged out successfully']);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
