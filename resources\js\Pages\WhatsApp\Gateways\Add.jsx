import NoRecord from "@/Components/HelperComponents/NoRecord";
import useFetch from "@/Global/useFetch";
import { input_custom, radio_custom } from "@/Pages/Helpers/DesignHelper";
import { useForm } from "@inertiajs/react";
import { Button, Checkbox, Label, Radio, TextInput } from "flowbite-react";
import { useEffect, useState } from "react";
import { IoMdAdd } from "react-icons/io";

export default function Add({ onClose }) {
    const { data, setData, post, processing, errors } = useForm({
        name: "",
        randomDelay: 1,
        randomDelayFrom: 0,
        randomDelayTo: 0,
        delaySeconds: 0,
        webhookUrl: "",
        savechat: 0,
        isAutoReply: 0,
        autoreply: null,
        // isApi: 0,
        isDefault: false
    });
    const { data: fetchBots, loading: fetchBotsLoading, error } = useFetch(route('whatsapp.bot.index', { all: true }));
    // console.log()



    function formDataHandle(e) {
        e.preventDefault();

        post(route("whatsapp.gateway.store"), {
            onSuccess: () => {
                onClose(false);
            },
        });
    }
    return (
        <form
            className="p-2 px-3 bg-white rounded-lg"
            onSubmit={formDataHandle}
        >

            <div className="flex flex-col gap-4">
                <div className="">
                    <label>
                        <div className="mb-2 text-sm font-medium">
                            Name&nbsp;
                            <span className="text-red-600">*</span>
                        </div>
                    </label>
                    <div className="">
                        <TextInput
                            theme={input_custom}
                            color={errors.name ? "failure" : "gray"}
                            type="text"
                            placeholder="Name"
                            required
                            className="w-full"
                            name="name"
                            helperText={errors.name && errors.name}
                            onChange={(e) =>
                                setData("name", e.target.value)
                            }
                        />
                    </div>
                </div>

                <div className="">
                    <label>
                        <div className="mb-2 text-sm font-medium">
                            Webhook URL&nbsp;
                        </div>
                    </label>
                    <div className="w-full">
                        <TextInput
                            theme={input_custom}
                            name="webhookUrl"
                            placeholder="Webhook URL"
                            className="w-full"
                            helperText="This URL is called by POST method, when you receive any message on this WA account . Use the response with sender number and message"
                            onChange={(e) =>
                                setData("webhookUrl", e.target.value)
                            }
                        />
                    </div>
                </div>
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                    <div className="">
                        <label>
                            <div className="mb-2 text-sm font-medium">
                                Save Chat&nbsp;
                                <span className="text-red-600">*</span>
                            </div>
                        </label>
                        <div className="w-full">
                            <div className="flex flex-wrap items-center gap-3">
                                <div className="flex items-center gap-2 ">
                                    <Radio
                                        color={"blue"}
                                        theme={radio_custom}
                                        id="savechatYes"
                                        name="savechat"
                                        value="1"
                                        defaultChecked={data.savechat === 1}
                                        onChange={(e) =>
                                            setData("savechat", 1)
                                        }
                                    />
                                    <label htmlFor="savechatYes">
                                        <div className="text-sm text-gray-600">
                                            Yes&nbsp;
                                        </div>
                                    </label>
                                </div>
                                <div className="flex items-center gap-2 ">
                                    <Radio
                                        color={"blue"}
                                        theme={radio_custom}
                                        id="savechatNo"
                                        name="savechat"
                                        value="0"
                                        defaultChecked={data.savechat === 0}
                                        onChange={(e) =>
                                            setData("savechat", 0)
                                        }
                                    />
                                    <label htmlFor="savechatNo">
                                        <div className="text-sm text-gray-600">
                                            No&nbsp;
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="">
                        <label>
                            <div className="mb-2 text-sm font-medium">
                                Bot&nbsp;
                                <span className="text-red-600">*</span>
                            </div>
                        </label>
                        <div className="w-full">
                            <div className="flex flex-wrap gap-3">
                                <div className="flex items-center gap-2">
                                    <Radio
                                        color={"blue"}
                                        theme={radio_custom}
                                        id="isAutoReplyYes"
                                        name="isAutoReply"
                                        value={1}
                                        defaultChecked={
                                            data.isAutoReply === 1
                                        }
                                        onChange={(e) =>
                                            setData("isAutoReply", 1)
                                        }
                                    />
                                    <label htmlFor="isAutoReplyYes">
                                        <div className="text-sm text-gray-600">
                                            Yes&nbsp;
                                        </div>
                                    </label>
                                </div>
                                <div className="flex items-center gap-2 ">
                                    <Radio
                                        color={"blue"}
                                        theme={radio_custom}
                                        id="isAutoReplyNo"
                                        name="isAutoReply"
                                        value={0}
                                        defaultChecked={
                                            data.isAutoReply === 0
                                        }
                                        onChange={(e) =>
                                            setData("isAutoReply", 0)
                                        }
                                    />
                                    <label htmlFor="isAutoReplyNo">
                                        <div className="text-sm text-gray-600">
                                            No&nbsp;
                                        </div>
                                    </label>
                                </div>
                            </div>
                            {errors.isAutoReply ? (
                                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                                    {errors.isAutoReply}
                                </p>
                            ) : (
                                <></>
                            )}
                        </div>
                    </div>
                </div>
                {data.isAutoReply === 1 ? (
                    <div className="">
                        <label>
                            <div className="mb-2 text-sm font-medium">
                                Bot&nbsp;
                                <span className="text-red-600">*</span>
                            </div>
                        </label>
                        <div className="w-full">
                            <div className="grid flex-wrap grid-cols-1 gap-3 lg:grid-cols-3 md:grid-cols-2">
                                {fetchBots?.collection?.bots.length > 0 ? (
                                    fetchBots?.collection?.bots.map((info, k) => (
                                        <div
                                            className="flex items-center gap-2 "
                                            key={k}
                                        >
                                            <Radio
                                                color={"blue"}
                                                theme={radio_custom}
                                                id={"autoreply-rule" + k}
                                                name="autoreply"
                                                value={info.id}
                                                onChange={(e) =>
                                                    setData(
                                                        "autoreply",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <label
                                                className="text-sm"
                                                htmlFor={
                                                    "autoreply-rule" + k
                                                }
                                            >
                                                <div className="text-sm text-gray-600">
                                                    {info.name}&nbsp;
                                                </div>
                                            </label>
                                        </div>
                                    ))
                                ) : (
                                    <NoRecord loading={fetchBotsLoading} type="circle" />
                                )}
                            </div>
                        </div>
                        {errors.autoreply ? (
                            <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                                {errors.autoreply}
                            </p>
                        ) : (
                            <></>
                        )}
                    </div>
                ) : (
                    <></>
                )}
                <div className="">
                    <label>
                        <div className="mb-2 text-sm font-medium">
                            Delay type&nbsp;
                            <span className="text-red-600">*</span>
                        </div>
                    </label>
                    <div className="w-full">
                        <div className="flex flex-wrap gap-3 ">
                            <div className="flex items-center gap-2 ">
                                <Radio
                                    color={"blue"}
                                    theme={radio_custom}
                                    id="delayRandom"
                                    name="randomDelay"
                                    value={data.randomDelay}
                                    defaultChecked={data.randomDelay === 1}
                                    onChange={(e) =>
                                        setData(
                                            "randomDelay",
                                            1
                                        )
                                    }
                                />
                                <label htmlFor="delayRandom">
                                    <div className="text-sm text-gray-600">
                                        Random&nbsp;
                                    </div>
                                </label>
                            </div>
                            <div className="flex items-center gap-2 ">
                                <Radio
                                    color={"blue"}
                                    theme={radio_custom}
                                    id="delayFixed"
                                    name="randomDelay"
                                    value={0}
                                    defaultChecked={data.randomDelay === 0}
                                    onChange={(e) =>
                                        setData(
                                            "randomDelay",
                                            0
                                        )
                                    }
                                />
                                <label htmlFor="delayFixed">
                                    <div className="text-sm text-gray-600">
                                        Fixed&nbsp;
                                    </div>
                                </label>
                            </div>
                        </div>
                        {errors.randomDelay ? (
                            <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                                {errors.randomDelay}
                            </p>
                        ) : (
                            <></>
                        )}
                    </div>

                </div>
                {data.randomDelay == 1 &&
                    <div className="flex items-center gap-2 ">
                        <div>Delay ranges from </div>
                        <div>
                            <TextInput
                                sizing="sm"
                                theme={input_custom}
                                id="PrincipalPaid1"
                                type="number"
                                placeholder="0"
                                min={0}
                                required
                                className="min-w-12 max-w-16"
                                value={data.randomDelayFrom}
                                onChange={(e) => setData('randomDelayFrom', e.target.value)}
                            />
                        </div>
                        <div>To</div>
                        <div>
                            <TextInput
                                sizing="sm"
                                theme={input_custom}
                                id="PrincipalPaid2"
                                type="number"
                                placeholder="0"
                                min={0}
                                required
                                className="min-w-12 max-w-16"
                                value={data.randomDelayTo}
                                onChange={(e) => setData('randomDelayTo', e.target.value)}
                            />
                        </div>

                        <div>
                            seconds. <span className="text-red-600">*</span>
                        </div>
                    </div>
                }

                {data.randomDelay == 0 &&
                    <div className="flex items-center gap-2 ">
                        <div>Delay of</div>
                        <div>
                            <TextInput
                                sizing="sm"
                                theme={input_custom}
                                id="PrincipalPaid3"
                                type="number"
                                min={data.delaySeconds}
                                placeholder="0"
                                required
                                className="text-center min-w-12 max-w-16"
                                value={data.delaySeconds}
                                onChange={(e) => setData('delaySeconds', e.target.value)}
                            />
                        </div>
                        <div>
                            seconds. <span className="text-red-600">*</span>
                        </div>
                    </div>
                }
                {errors.delaySeconds ? (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {errors.delaySeconds}
                    </p>
                ) : (
                    <></>
                )}
                {errors.randomDelayFrom ? (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {errors.randomDelayFrom}
                    </p>
                ) : (
                    <></>
                )}
                {errors.randomDelayTo ? (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {errors.randomDelayTo}
                    </p>
                ) : (
                    <></>
                )}
                <div className="flex items-center gap-2">
                    <Checkbox color="blue" id="SetAsDefaultGateway" checked={data.isDefault} onChange={() => setData('isDefault', !data.isDefault)} />
                    <Label htmlFor="SetAsDefaultGateway">Set as default gateway!</Label>
                </div>
            </div>

            <div className="flex justify-end mx-5 my-3">
                <Button
                    isProcessing={processing}
                    disabled={processing}
                    color="blue"
                    type="submit"
                    size={"sm"}
                    onSubmit={formDataHandle}
                >
                    <div className="flex items-center gap-1">
                        <IoMdAdd className="text-lg" />
                        Add
                    </div>
                </Button>
            </div>
        </form>
    );
}
