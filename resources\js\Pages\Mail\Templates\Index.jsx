
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import {
    button_custom,
    card_custom,
    customDrawer
} from "@/Pages/Helpers/DesignHelper";
import { Head, router } from "@inertiajs/react";
import { <PERSON><PERSON>, Card, Drawer, Popover } from "flowbite-react";
import { useCallback, memo } from "react";
import { CgAttachment } from "react-icons/cg";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoIosArrowDown, IoMdAdd } from "react-icons/io";
import {
    MdDeleteOutline,
    MdOutlineEdit
} from "react-icons/md";
import { RiFileSearchLine } from "react-icons/ri";
import MailMain from "../MailMain";
import Add from "./Add";
import Category from "./Category";
import Edit from "./Edit";
import View from "./View";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import TinyEditor from "@/Components/TinyEditor";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import { useTemplateState } from "./hooks/useTemplateState";

// Memoized Template Card Component
const TemplateCard = memo(function TemplateCard({
    template,
    ky,
    can_view,
    can_edit,
    can_delete,
    onView,
    onEdit,
    onDelete
}) {
    return (
        <Card key={'card-' + ky}
            theme={card_custom}
            className="relative w-full bg-slate-300"
        >
            {template.body != null &&
                <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
            }
            <div className="flex flex-col justify-between h-full">
                <div className="p-2 overflow-auto">
                    {template.body != null &&
                        <TinyEditor key={ky} readOnly value={template.body} id={"tinyEditor-" + ky} />
                    }
                    {template.file &&
                        <div className="flex items-center gap-1 px-2">
                            <span className="text-sm text-blue-600">
                                <CgAttachment />
                            </span>
                            <span className="text-sm text-blue-600">
                                1
                            </span>
                        </div>
                    }
                </div>
                <div className="p-1 bg-slate-100">
                    <div className="flex items-center justify-between gap-2 rounded-b-lg">
                        <div className="truncate">
                            <div className="text-sm">
                                {template.name}
                            </div>
                            <div className="text-xs text-slate-500">
                                {template.mail_template_category_name}
                            </div>
                        </div>
                        <div className="flex items-center">
                            {can_view &&
                                <Button
                                    theme={button_custom}
                                    color="withoutBorder"
                                    size="xs"
                                    onClick={() => onView(template)}
                                >
                                    <RiFileSearchLine className="text-slate-400" />
                                </Button>
                            }
                            {can_edit &&
                                <Button
                                    theme={button_custom}
                                    color="withoutBorder"
                                    size="xs"
                                    onClick={() => onEdit(template)}
                                >
                                    <MdOutlineEdit className="text-slate-400" />
                                </Button>
                            }
                            {can_delete &&
                                <Button
                                    theme={button_custom}
                                    color="withoutBorder"
                                    size="xs"
                                    onClick={() => onDelete(template)}
                                >
                                    <MdDeleteOutline className="text-slate-400" />
                                </Button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
});

export default function Index({ collection }) {

    const { templates, getData, can_add, can_edit, can_delete, can_view } = collection;

    // Use the custom hook for state management
    const {
        selectedCategory,
        modalState,
        selectedTemplates,
        isMultipleDeleteConfirmOpen,
        filteredTemplates,
        handleCategorySelect,
        handleTemplateView,
        handleTemplateEdit,
        handleTemplateDelete,
        handleMultipleDelete: handleMultipleDeleteHook,
        closeAddModal,
        closeEditModal,
        closeViewModal,
        openAddModal,
        closeDeleteConfirm
    } = useTemplateState(templates);

    const deleteRecords = useCallback(() => {
        if (selectedTemplates.length > 0) {
            router.delete(route("mail.templates.destroy", { template: selectedTemplates.toLocaleString() }));
        }
    }, [selectedTemplates]);

    const handleMultipleDelete = useCallback((res) => {
        const templatesToDelete = handleMultipleDeleteHook(res);
        if (templatesToDelete) {
            deleteRecords();
        }
    }, [handleMultipleDeleteHook, deleteRecords]);
    return (
        <MailMain>
            <>
                <Head title="Mail-Templates" />

                <div className="h-full pt-3 ">
                    <div className="grid h-full grid-flow-row-dense grid-cols-11 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                        <div className="hidden h-full bg-white rounded-lg xl:col-span-2 lg:col-span-3 md:col-span-2 sm:col-span-3 lg:flex">
                            <Category
                                setSelectedCategory={handleCategorySelect}
                                selectedCategory={selectedCategory}
                            />
                        </div>
                        <div className="relative h-full p-2 pt-0 bg-white rounded-lg dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                            <div>
                                {/* Welcome message  */}
                                <div className="flex items-center justify-between p-2 ">
                                    <div className="flex items-center gap-2">

                                        <div className="flex lg:hidden">
                                            <Popover
                                                aria-labelledby="Category-popover"
                                                content={
                                                    <Category
                                                        setSelectedCategory={handleCategorySelect}
                                                        selectedCategory={selectedCategory}
                                                    />
                                                }
                                            >
                                                <Button color="gray" size="xs" className="items-center group">Category
                                                    <IoIosArrowDown className="transition delay-150 self-center text-md ms-2 group-aria-[expanded=false]:rotate-180" />
                                                </Button>
                                            </Popover>
                                        </div>
                                    </div>
                                    <div>
                                        {can_add &&
                                            <Button
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                                onClick={openAddModal}
                                            >
                                                <div className="flex items-center gap-1 text-xs">
                                                    <IoMdAdd className="text-sm text-slate-500" />
                                                    <span>Add</span>
                                                </div>
                                            </Button>
                                        }
                                    </div>
                                </div>

                                <div className="h-full pb-2 md:px-2">
                                    <div className="grid grid-flow-row-dense grid-cols-1 gap-4 2xl:grid:cols-2 xl:grid-cols-2 lg:grid-cols-1 md:grid-cols-1 sm:grid-cols-1">
                                        {filteredTemplates?.data?.length > 0 ?
                                            filteredTemplates?.data?.map((template, ky) =>
                                                <TemplateCard
                                                    key={'template-' + template.id}
                                                    template={template}
                                                    ky={ky}
                                                    can_view={can_view}
                                                    can_edit={can_edit}
                                                    can_delete={can_delete}
                                                    onView={handleTemplateView}
                                                    onEdit={handleTemplateEdit}
                                                    onDelete={handleTemplateDelete}
                                                />
                                            )
                                            :
                                            <NoRecord />
                                        }
                                    </div>
                                </div>
                                <div className="bottom-0 w-full mt-1.5 p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                                    <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                                        <div className="flex items-center gap-4">
                                            <PerPageDropdown
                                                getDataFields={
                                                    getData ?? null
                                                }
                                                routeName={
                                                    "mail.templates.index"
                                                }
                                                data={templates}
                                                customPerPage={10}
                                            />
                                        </div>
                                        {templates?.links &&
                                            <Paginate tableData={templates} />
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {(modalState.openAdd && can_add) && (
                    <Drawer
                        theme={customDrawer}
                        open={modalState.openAdd}
                        onClose={closeAddModal}
                        position="right"
                        className="w-full lg:w-5/6 md:w-4/5"
                    >
                        <Drawer.Header
                            titleIcon={HiOutlineTemplate}
                            title="Add Template"
                        />
                        <Drawer.Items>
                            <Add
                                onClose={closeAddModal}
                            />
                        </Drawer.Items>
                    </Drawer>
                )}
                {(modalState.openEdit && can_edit) && (
                    <Drawer
                        theme={customDrawer}
                        open={modalState.openEdit}
                        onClose={closeEditModal}
                        position="right"
                        className="w-full lg:w-1/2 md:w-4/5"
                    >
                        <Drawer.Header
                            titleIcon={HiOutlineTemplate}

                            title={"Edit Template (" + modalState.selected.name + ")"}
                        />
                        <Drawer.Items>
                            <Edit

                                onClose={closeEditModal}
                                template={modalState.selected}
                            />
                        </Drawer.Items>
                    </Drawer>
                )}
                {(modalState.openView && can_view) &&
                    <Drawer
                        theme={customDrawer}
                        open={modalState.openView}
                        onClose={() => handleModalStateChange({ openView: !modalState.openView })}
                        position="right"
                        className="w-full lg:w-1/2 md:w-2/3"

                    >
                        <Drawer.Header
                            title={"View Template (" + modalState.selected.name + ")"}
                            titleIcon={RiFileSearchLine}

                        />
                        <Drawer.Items>
                            <View
                                onClose={() => handleModalStateChange({ openView: !modalState.openView })}
                                template={modalState.selected}
                            />
                        </Drawer.Items>
                    </Drawer>
                }
                {(can_delete && isMultipleDeleteConfirmOpen) &&
                    <ConfirmBox
                        isOpen={isMultipleDeleteConfirmOpen}
                        onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                        onAction={handleMultipleDelete} // Handle the user's choice
                        title="Delete Template "
                        message="Do you want to Delete Template."
                        confirmText="Yes, Delete!"
                        cancelText="No, Keep It"
                        confirmColor="orange"
                        cancelColor="gray"
                    />
                }
            </>
        </MailMain >
    );
}
