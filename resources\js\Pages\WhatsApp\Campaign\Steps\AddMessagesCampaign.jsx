import { FilePreview } from "@/Components/FilePreview";
import MessageBox from "@/Components/HelperComponents/MessageBox";
import TemplateBox from "@/Components/TemplateBox";
import { button_custom, card_custom } from "@/Pages/Helpers/DesignHelper";
import { fetchJson } from "@/Pages/Helpers/Helper";
import { WaBaExtractVariables } from "@/Pages/Helpers/WabaHelper";
import { useForm } from "@inertiajs/react";
import { Button, Card, FileInput, Label, Select } from "flowbite-react";
import { useEffect, useState, memo } from "react";
import { IoMdClose } from "react-icons/io";
import { MdOutlineAdd } from "react-icons/md";

const VARIABLE_OPTIONS = {
    name: 'name',
    mobile: 'mobile',
    email: 'email',
    var1: 'var1',
    var2: 'var2',
    va3: 'va3',
    var4: 'var4',
    var5: 'var5'
};


function AddMessagesCampaign({ campaignId, updateMessages }) {
    const [clearAll, setClearAll] = useState(0);
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [selectedTemplateData, setSelectedTemplateData] = useState(null);
    const [variables, setVariables] = useState(null);
    const [templateImage, setTemplateImage] = useState(null);
    const TemplatePreview = memo(({ selectedTemplateData, handleTemplateClose, setData, isImage }) => (
        <div className="flex gap-4">
            <div className="w-1/3">
                <Card theme={card_custom} className="justify-between">
                    <TemplateBox>
                        <div className="absolute top-0 right-0 p-2">
                            <Button theme={button_custom} size="xs" color="red" onClick={handleTemplateClose}>
                                <IoMdClose />
                            </Button>
                        </div>
                        <div>
                            {
                                data.attachment ?
                                    <FilePreview object={data.attachment[0]} imageSize="lg" width="w-fit" />
                                    :
                                    selectedTemplateData.file && <FilePreview object={selectedTemplateData.file} imageSize="lg" width="w-fit" />
                            }
                            {selectedTemplateData.body && <p className="text-wrap">{data.msg}</p>}
                            <div className="text-xs text-end text-slate-500">09:45</div>
                        </div>
                    </TemplateBox>
                </Card>
            </div>
            {isImage && (
                <div className="flex flex-col max-w-md gap-4 mb-2 capitalize">
                    <div>
                        <Label htmlFor="file-upload" value="Upload file" />
                        <FileInput id="file-upload" onChange={(e) => setData('attachment', e.target.files)} />
                    </div>
                </div>
            )}
        </div>
    ));

    const { data, setData, post, processing, errors } = useForm({
        msg: "",
        campaign_id: campaignId,
        files: null,
        varObject: null,
        template_id: '',
        attachment: {}
    });
    console.log(data);

    useEffect(() => {
        setData('attachment', data.files);
    }, [data.files]);

    useEffect(() => {
        setVariables(WaBaExtractVariables(data.msg));
    }, [data.msg]);

    useEffect(() => {
        if (variables) {
            setData('variable_count', Object.keys(variables).length);
        }
    }, [variables]);

    const [tempData, setTempData] = useState('');
    console.log(
        tempData
    );
    useEffect(() => {
        setData('msg', tempData);
    }, [tempData]);
    useEffect(() => {
        if (!selectedTemplate) return;

        fetchJson(route("helper.getTemplate", { id: selectedTemplate }), {}, true)
            .then(content => {
                if (!content.status) return;
                console.log(content);
                console.log(content.template.body);

                setClearAll(prev => prev + 1);
                setVariables(WaBaExtractVariables(content.template.body));
                setData({
                    msg: content.template.body,
                    files: null,
                    attachment: null,
                    campaign_id: campaignId,
                    template_id: selectedTemplate
                });
                setTempData(content.template.body)
                setTemplateImage(content.template.file);
                setSelectedTemplateData(content.template);

            })
            .catch(error => console.error("Error fetching template:", error));
    }, [selectedTemplate]);

    const handleVariableChange = (key, sub_key, value) => {
        setData(prev => ({
            ...prev,
            varObject: {
                ...prev.varObject,
                [key]: {
                    [sub_key === 'column' ? 'column' : 'fixed']: value,
                    [sub_key === 'column' ? 'fixed' : 'column']: ""
                }
            }
        }));
    };

    const addCampaignMessage = () => {
        post(route("whatsapp.campaign.message.store"), {
            onSuccess: () => {
                setData({
                    msg: "",
                    attachment: null,
                    campaign_id: campaignId,
                    files: null,
                    template_id: null
                });
                updateMessages();
                setClearAll(prev => prev + 1);
                setSelectedTemplateData(null);
            }
        });
    };

    const handleTemplateClose = () => {
        setSelectedTemplateData(null);
        setSelectedTemplate(null);
        setData(prev => ({
            ...prev,
            files: null,
            attachment: null,
            campaign_id: campaignId,
            template_id: null
        }));
        setClearAll(prev => prev + 1);
    };

    return (
        <div>
            <form>
                <div className="flex flex-col gap-1">
                    <div className="mb-2">
                        <MessageBox
                            isOfficial
                            setTemp={setSelectedTemplate}
                            data={data.msg}
                            setData={setData}
                            errors={errors}
                            fileUpload={true}
                            allData={data}
                            clearAll={clearAll}
                            enableTemplate={true}
                        />
                    </div>
                    <div className="flex flex-col max-w-md gap-4 mb-2">
                        {variables && Object.entries(variables).map(([key]) => (
                            <div key={`temp-inputs-${key}`}>
                                <div className="block mb-2">
                                    <Label htmlFor={`var-id-${key}`} className="uppercase" value={key} />
                                </div>
                                <div className="flex gap-3">
                                    {/* <TextInput
                                    className="w-full"
                                    id={`var-id-${key}`}
                                    type="text"
                                    sizing="sm"
                                    placeholder={key}

                                    disabled={data.varObject && data.varObject[key] && data.varObject[key]['column'].length > 0}
                                    onChange={(e) => handleVariableChange(key, "fixed", e.target.value)}
                                /> */}
                                    <Select
                                        onChange={(e) => handleVariableChange(key, "column", e.target.value)}
                                        sizing="sm"
                                        disabled={data.varObject?.[key]?.['fixed']?.length > 0}
                                    >
                                        <option value="">Select Column</option>
                                        {Object.entries(VARIABLE_OPTIONS).map(([optKey, value]) => (
                                            <option key={optKey} value={value}>{value}</option>
                                        ))}
                                    </Select>
                                </div>
                                {errors[key] && <div className="text-red-600">{errors[key]}</div>}
                            </div>
                        ))}
                        {errors.variable_error && <div className="text-red-500">{errors.variable_error}</div>}
                    </div>

                    {selectedTemplateData && (
                        <TemplatePreview
                            selectedTemplateData={selectedTemplateData}
                            handleTemplateClose={handleTemplateClose}
                            setData={setData}
                            isImage={templateImage}
                        />
                    )}

                    <div className="mb-2">
                        <div className="flex justify-end">
                            <Button
                                theme={button_custom}
                                size="xs"
                                isProcessing={processing}
                                className="rounded-md"
                                color="blue"
                                type="button"
                                onClick={addCampaignMessage}
                            >
                                <div className="flex items-center gap-1">
                                    <MdOutlineAdd className="text-lg" />
                                    <span className="text-sm">Add Message</span>
                                </div>
                            </Button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    );
}

export default AddMessagesCampaign;
