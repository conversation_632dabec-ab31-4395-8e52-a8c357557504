<?php

namespace App\Http\Controllers;

use App\Models\Settings;
use App\Models\WaMsgs;
use PDOException;
use App\Models\WaChannel;
use App\Models\ContactList;
use App\Models\User;
use App\Models\WaBot;
use App\Models\WaManualSending;
use App\Models\WaMedia;
use App\Models\WaTemplate;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class HelperController extends Controller
{
    public function changeFlag(Request $r)
    {
        try {
            DB::table($r->table)->where('id', $r->id)->update([$r->column => $r->flag]);
        } catch (PDOException $e) {

            session()->flash('message', errorMessage($e->errorInfo[1]));
            session()->flash("type", "failure");
            return false;
        }
    }
    public function singleDelete(Request $r)
    {

        try {
            
            DB::table($r->table)->where('id', $r->id)->delete();
            session()->flash('message', $r->message ?? "Record Deleted");
            session()->flash("type", "success");
            // return redirect()->back()->with(["message" => "Channel removed.", "type" => "success"]);
        } catch (PDOException $e) {
            
            if ($e->getCode() == 23000) {
                return redirect()->back()->with(["message" => errorMessage(5), "type" => "failure"]);
            }
            return redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
            // session()->flash('message', $e->errorInfo[2]);
            // session()->flash("type", "failure");
            // return false;
        }
    }
    // Do not use this in future
    public function getChannels()
    {
        try {

            $data['channels'] = WaChannel::where('user_id', Auth::id())
                ->paginate(2);
            $data['items'] = $data['channels'];
            $data['status'] = true;
            return response()->json($data);
        } catch (PDOException $e) {
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
            $data['status'] = false;
            $data['message'] = errorMessage(1);
            return response()->json($data);
        }
    }

    // Do not use this in future
    public function getAllChannels()
    {
        try {
            $data['channels'] = WaChannel::where('user_id', Auth::id())
                ->get();
            return response()->json($data);
        } catch (PDOException $e) {
            
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
            return false;
        }
    }
    public function getContactList()
    {
        try {
            $data['contactList'] = ContactList::where('user_id', Auth::id())
                ->paginate(2);
            $data['items'] = $data['contactList'];
            return response()->json($data);
        } catch (PDOException $e) {
            
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
            return false;
        }
    }
    function getAutoReply()
    {
        $data["autoReply"] = WaBot::whereIn('user_id', function ($query) {
            $query->select('id')
                ->from((new User())->getTable())
                ->whereRaw('id = ?', [Auth::id()])
                ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
        })->get();
        return response()->json($data);
    }
    // function getMedia(Request $request)
    // {
    //     $ids = explode(",", $request->ids);
    //     $mediaData = WaMedia::whereIn("id", $ids)->get();
    //     return response()->json($mediaData);
    // }
    function getChat($person = null)
    {
        try {
            $data['chat'] = WaMsgs::paginate(10);

            return response()->json($data);
        } catch (PDOException $e) {
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
            return false;
        }
    }
    function getPeoples()
    {
        try {
            $manualSendingData = WaManualSending::where('user_id', Auth::id())
                // ->where('messageDateTime', '<', Carbon::now())
                ->orderBy('messageDateTime', 'desc')->with('channel')
                ->get();

            $waMsgsData = WaMsgs::where('user_id', Auth::id())
                // ->where('messageDateTime', '<', Carbon::now())
                ->orderBy('messageDateTime', 'desc')->with('channel')
                ->get();
            
            $data['people'] = null;
            // Combine and ensure uniqueness
            if ($manualSendingData->isNotEmpty() || $waMsgsData->isNotEmpty()) {
                $data['people'] = $manualSendingData
                    ->map(function ($row) {
                        return [
                            'id' => $row->id,
                            'unique_key' => $row->mobile, // Set mobile as unique key
                            'data' => $row->toArray(), // Store the original row data
                        ];
                    })
                    ->merge(
                        $waMsgsData->map(function ($row) {
                            return [
                                'id' => $row->id,
                                'unique_key' => explode('@', $row->remoteJid)[0], // Extract unique part of remoteJid
                                'data' => $row->toArray(), // Store the original row data
                            ];
                        })
                    )
                    ->unique('unique_key') // Remove duplicates based on unique_key
                    ->pluck('data') // Retrieve only the original row data
                    ->values() // Reindex the collection
                    ->toArray(); // Convert to array if needed

            }

            return response()->json($data['people']);
        } catch (Exception $e) {
            return response()->json(['status' => 'fail']);
        }
    }
    function getTemplate($id)
    {
        try {

            $data = WaTemplate::find($id);
            if ($data != null) {

                return response()->json(['status' => true, 'template' => $data]);
            }
            return response()->json(['status' => false, 'message' => errorMessage(1)]);
        } catch (\Throwable $e) {
            return response()->json(['status' => false, 'message' => errorMessage(1)]);
        }
    }

    function getSettings()
    {
        try {
            $data = Settings::all()->pluck('value', 'key');
            if ($data != null) {
                return response()->json(['status' => true, 'data' => $data]);
            }
            return response()->json(['status' => false, 'message' => errorMessage(1)]);
        } catch (\Throwable $e) {
            return response()->json(['status' => false, 'message' => errorMessage(1)]);
        }
    }
}
