<?php

namespace App\Http\Controllers\WhatsappBusiness;

use App\Http\Controllers\Controller;

use App\Models\WhatsappBusiness\bot\WaBaBot;
use App\Models\WhatsappBusiness\WaBaCampaign;
use App\Models\WhatsappBusiness\WaBaGateway;

use App\Models\WhatsappBusiness\WaBaTemplateView;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class WhatsAppController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    public function index()
    {
        $userId = Auth::id();
        $campaigns =  WaBaCampaign::where('user_id', $userId)->get();
        $data['totalCampaign'] = $campaigns->count();
        $data['draft'] = $campaigns->where('status', 0)->count();
        $data['started'] = $campaigns->where('status', 1)->count();
        $data['scheduled'] = $campaigns->where('sendAfterDateTime', ">", now())->count();
        $data['paused'] = $campaigns->where('status', 2)->count();
        $data['completed'] = $campaigns->where('status', 3)->count();
        $data['terminated'] = $campaigns->where('status', 4)->count();
        return Inertia::render('WhatsappBusiness/Dashboard', ['collection' => $data]);
    }
    // public function fetchCountData()
    // {
    //     $data = [];
    //     try {
    //         $wa_user_counts = DB::table('waba_user_counts')->where('user_id', Auth::id())->first();
    //         $data["campaigns"] = $wa_user_counts->campaign;
    //         $data["autoReply"] = $wa_user_counts->bots;
    //         $data["templates"] = $wa_user_counts->template;
    //         $data["gateway"] = $wa_user_counts->gateway;
    //         return response()->json($data);
    //     } catch (Exception $e) {
    //         return response()->json($data);
    //     }
    // }


    public function menuItems()
    {
        $data = [];
        try {
            $wa_user_counts = DB::table('waba_counts')->where('user_id', Auth::id())->first();
            $data["campaigns"] = $wa_user_counts->campaign;
            $data["bots"] = $wa_user_counts->bots;
            $data["templates"] = $wa_user_counts->template;
            $data["gateways"] = $wa_user_counts->gateway;
        } catch (Exception $e) {
            
        }
        return response()->json($data);
    }



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
