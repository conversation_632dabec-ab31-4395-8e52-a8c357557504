<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WaBot;
use App\Models\WaChannel;
use App\Models\WaMsgs;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use PDOException;

class GatewayController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaChannel())->getTable();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tableName = $this->tableName;
        if (!request()->user()->can("$tableName.index")) {
            abort(403, "unauthorized");
        }

        try {

            $column = request()->query('column', 'id');
            $sortBy = request()->query('sort', 'desc');
            $request = request();
            $getUser = request()->query('userID');

            $channels = WaChannel::query();
            $channels = $channels->orderBy($column, $sortBy);

            // $getusersData = request()->user()->getAllChildUsers();

            if ($getUser) {
                $user = User::find($getUser);
                $userIds = request()->user()->getAllChildUserIds($getUser);
                if (request()->user()->isAdmin() || in_array($user->id, $userIds->toArray())) {
                    $channels = $channels->whereIn('user_id', $userIds);
                } else {
                    return (request()->header('Accept') == 'application/json')
                        ? response()->json(['message' => 'Invalid request.', 'status' => true])
                        : redirect()->back()->with(["message" => 'Invalid request.', "type" => "failure"]);
                }
            } else {
                $userIds = request()->user()->getAllChildUserIds();
                $channels = $channels->whereIn("user_id", $userIds);
            }

            $channels = $channels->with('user');

            $channels = $request->query('all', false)
                ?  $channels->get()
                :  $channels->paginate($request->query('perPage', 10))->withQueryString();

            return (request()->header('Accept') == 'application/json') ?
                response()->json([
                    'message' => "Gateway List.",
                    'status' => true,
                    'collection' => [
                        'channels' => $channels,
                        // 'users' => $getusersData,
                        'getData' => $_GET,
                        "can_add" => request()->user()->can("$tableName.add"),
                        "can_edit" => request()->user()->can("$tableName.edit"),
                        "can_delete" => request()->user()->can("$tableName.delete"),
                    ]
                ])
                : Inertia::render('WhatsApp/Gateways/Index', [
                    'collection' => [
                        'channels' => $channels,
                        // 'users' => $getusersData,
                        'getData' => $_GET,
                        "can_add" => request()->user()->can("$tableName.add"),
                        "can_edit" => request()->user()->can("$tableName.edit"),
                        "can_delete" => request()->user()->can("$tableName.delete"),
                    ]
                ]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!request()->user()->can("$this->tableName.add")) {
            abort(403, "unauthorized");
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        if (!request()->user()->can("$this->tableName.add")) {
            abort(403, "unauthorized");
        }
        $request->validate(
            [
                'name' => 'required|min:3|max:20',
                'isAutoReply' => 'required',
                'webhookUrl'=>'nullable|max:255',
                'autoreply' => 'required_if:isAutoReply,"1"|nullable',
                'delaySeconds' => 'required_if:randomDelay,0|numeric',
                'randomDelayFrom' => 'required_if:randomDelay,1|numeric',
                'randomDelayTo' => 'required_if:randomDelay,1|numeric',
            ],
            [
                'autoreply.required_if' => 'Please Select AutoReply'
            ],
            [
                'webhookUrl'=>'Webhook URL',
                'name' => 'Name',
                'autoreply' => 'Auto Replies',
                'randomDelayFrom' => 'Delay From',
                'randomDelayTo' => 'Delay To',
                'randomDelay' => 'Delay Type',
                'delaySeconds' => 'Fix Delay',
                'savechat' => 'Save Chat',
                'isAutoReply' => 'Auto Reply',
            ]
        );
        try {
            $Channel = new WaChannel();
            $Channel->name = $request->name;
            $Channel->randomDelay = $request->randomDelay;
            if ($request->randomDelay) {
                $Channel->randomDelayFrom = $request->randomDelayFrom;
                $Channel->randomDelayTo = $request->randomDelayTo;
            } else {
                $Channel->randomDelayFrom = 0;
                $Channel->randomDelayTo = 0;
                $Channel->delaySeconds = $request->delaySeconds;
            }
            $Channel->savechat = $request->savechat;
            $Channel->webhookUrl = $request->webhookUrl;

            if ($request->isAutoReply == 1) {
                $Channel->wa_bot_id = $request->autoreply;
            }
            if ($request->isDefault) {
                $wChannels = WaChannel::where(['user_id' => Auth::id(), 'isDefault' => 1])->update(['isDefault' => 0]);
            }
            $Channel->isDefault = (int) $request->isDefault;
            $Channel->user_id = Auth::id();
            $Channel->save();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "GateWay Created.", 'status' => true])
                :
                redirect()->back()->with(["message" => "GateWay Created.", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (!request()->user()->can("$this->tableName.view")) {
            abort(403, "unauthorized");
        }
    }

    /**
     * Show the form for editing the specified resource.
     */

    public function edit(string $id)
    {
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }

        try {
            $userId = $_GET["userID"] ?? Auth::id();
            $backData['channel'] = WaChannel::where('id', $id)->first();
            
            if ($backData['channel']) {
                $backData['autoReplies'] = WaBot::whereIn('user_id', function ($query) use ($userId) {
                    $query->select('id')
                        ->from((new User())->getTable())
                        ->whereRaw('id = ?', [$userId])
                        ->orWhereRaw('parentUser_id = ?', [$userId]);
                })->get();
                return response()->json($backData);
            } else {
                session()->flash('message', 'Record Not Found.');
                session()->flash("type", "failure");
                return redirect()->back();
            }
        } catch (Exception $e) {
            
            session()->flash('message', errorMessage(4));
            session()->flash("type", "failure");
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }
        $request->validate(
            [
                'name' => 'required|min:3',
                'autoReply_id' => 'required_if:autoReply,1|nullable',
                'delaySeconds' => 'required_if:randomDelay,0|numeric',

            ],
            ['autoReply_id.required_if' => 'Please Select AutoReply'],
            [
                'name' => 'Name',
                'autoReply_id' => 'Auto Replies',
                'randomDelayFrom' => 'Delay From',
                'randomDelayTo' => 'Delay To',
                'randomDelay' => 'Delay Type',
                'delaySeconds' => 'Fix Delay',
                'savechat' => 'Save Chat',
                'isAutoReply' => 'Auto Reply',
                'webhookUrl' => 'WebHook Url'

            ]
        );

        try {
            $userId = $_GET["userID"] ?? Auth::id();
            $Channel = WaChannel::whereIn('user_id', function ($query) use ($userId) {
                $query->select('id')
                    ->from((new User())->getTable())
                    ->whereRaw('id = ?', [$userId])
                    ->orWhereRaw('parentUser_id = ?', [$userId]);
            })->where(['id' => $id])->first();
            
            if ($Channel) {
                $Channel->name = $request->name ?? $Channel->name;
                $Channel->randomDelay = $request->randomDelay ?? $Channel->randomDelay;
                if ($request->randomDelay) {
                    $Channel->randomDelayFrom = $request->randomDelayFrom ?? $Channel->randomDelayFrom;
                    $Channel->randomDelayTo = $request->randomDelayTo ?? $Channel->randomDelayTo;
                } else {
                    $Channel->randomDelayFrom = 0;
                    $Channel->randomDelayTo = 0;
                    $Channel->delaySeconds = $request->delaySeconds;
                }
                $Channel->savechat = $request->savechat;
                $Channel->webhookUrl = $request->webhookUrl;
                // $Channel->autoReply = $request->autoReply;
                if ($request->autoReply == 1) {
                    $Channel->wa_bot_id = $request->autoReply_id;
                } else {
                    $Channel->wa_bot_id = null;
                }
                if ($request->isDefault) {
                    WaChannel::where(['user_id' => Auth::id(), 'isDefault' => 1])->update(['isDefault' => 0]);
                }
                $Channel->isDefault = $request->isDefault;
                $Channel->user_id = Auth::id();
                $Channel->save();
                

                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Channel Updated.", 'status' => true])
                    : redirect()->back()->with(["message" => "Channel Updated.", "type" => "success"]);
            } else {

                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Not found.", 'status' => false])
                    :
                    redirect()->back()->with(["message" => "Not found.", "type" => "failure"]);
            }
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can("$this->tableName.delete")) {
            abort(403, "unauthorized");
        }

        try {
            $ids = explode(",", $id);

            $WaChannel = WaChannel::whereIn('id', $ids)->whereIn('user_id', function ($query) {
                $query->select('id')
                    ->from((new User())->getTable())
                    ->whereRaw('id = ?', [Auth::id()])
                    ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
            })->get();
            if (count($WaChannel) > 0) {
                WaChannel::whereIn('id', $ids)->whereIn('user_id', function ($query) {
                    $query->select('id')
                        ->from((new User())->getTable())
                        ->whereRaw('id = ?', [Auth::id()])
                        ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
                })->delete();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Gateway deleted.", 'status' => true])
                    : redirect()->back()->with(["message" => "Gateway deleted.", "type" => "success"]);
            } else {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Gateway NotFound.", 'status' => true])
                    : redirect()->back()->with(["message" => "Record Not Found.", "type" => "failure"]);
            }
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function updateDefault(Request $request)
    {
        try {
            $message = "Gateway Updated.";
            $campaign = WaChannel::find($request->id);
            if ($campaign) {
                $wChannels = WaChannel::where(['user_id' => Auth::id(), 'isDefault' => 1])->update(['isDefault' => 0]);
                $campaign->isDefault = $campaign->isDefault == 1 ? 0 : 1;
                $campaign->update();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => $message, 'status' => true])
                    : redirect()->back()->with(["message" => $message, "type" => "success"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Record Not Found.", 'status' => false])
                : redirect()->back()->with(["message" => "Record Not Found.", "type" => "failure"]);
        } catch (PDOException $e) {
            session()->flash('message', errorMessage($e->errorInfo[1]));
            session()->flash("type", "failure");
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->errorInfo[1]), 'status' => false])
                : redirect()->back();
        }
    }

    public function getChat($channel)
    {
        try {
            $savedChat = WaMsgs::where('wa_gateway_id', $channel)->with('user')->get();
            return response()->json(['chats' => $savedChat, 'count' => $savedChat->count()]);
        } catch (PDOException $e) {
            return redirect()->back()->with(["message" => $e->errorInfo[2], "type" => "failure"]);
        }
    }
}
