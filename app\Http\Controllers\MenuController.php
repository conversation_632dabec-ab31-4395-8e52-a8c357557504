<?php

namespace App\Http\Controllers;

class MenuController extends Controller
{
    public function navbarMenu()
    {
        $data = collect([
            [
                'name' => 'Whatsapp',
                'icon' => 'FaWhatsapp',
                'url' => route('whatsapp.index'),
                'bg' => 'bg-green-700',
                'textColor' => 'text-white',

                'routeForActive' => 'whatsapp.*',
                'requiredPermission' => 'whatsapp.view',
                'type' => 1,
                'order' => 2,
            ],
            [
                'name' => 'Contacts',
                'icon' => 'MdOutlineContacts',
                'url' => route("contactList.index"),
                'bg' => 'bg-pink-500',
                'textColor' => 'text-white',
                'routeForActive' => 'contactList*',
                'requiredPermission' => 'contactList.view',

                'type' => 1,
                'order' => 1,
            ],
            [
                'name' => 'Voice Call',
                'icon' => 'MdOutlineCall',
                'url' => route('call.index'),
                'bg' => 'bg-orange-500',
                'textColor' => 'text-white',
                'routeForActive' => 'call*',
                'requiredPermission' => 'call.view',

                'type' => 1,
                'order' => 3,
            ],
            [
                'name' => 'SMS',
                'icon' => 'TbMessageBolt',
                'url' => route('sms.index'),
                'bg' => 'bg-blue-700',
                'textColor' => 'text-white',
                'routeForActive' => 'sms*',
                'requiredPermission' => 'sms.view',

                'type' => 1,
                'order' => 4,
            ],
            [
                'name' => 'whatsapp Business',
                'icon' => 'FaWhatsapp',
                'url' => route('whatsappB.index'),
                'bg' => 'bg-yellow-400',
                'textColor' => 'text-white',
                'routeForActive' => 'whatsappB*',
                'requiredPermission' => 'whatsappB.view',

                'type' => 1,
                'order' => 3,
            ],
            [
                'name' => 'Telegram',
                'icon' => 'LiaTelegram',
                'url' => route('telegram.index'),
                'bg' => 'bg-sky-500',
                'textColor' => 'text-white',
                'routeForActive' => 'telegram*',
                'requiredPermission' => 'telegram.view',

                'type' => 1,
                'order' => 5,
            ],
            [
                'name' => 'whatsapp',
                'icon' => 'LuMailOpen',
                'url' => route('mail.dashboard'),
                'bg' => 'bg-pink-700',
                'textColor' => 'text-white',
                'routeForActive' => 'mail*',
                'requiredPermission' => 'mail.view',

                'type' => 1,
                'order' => 6,
            ],
            [
                'name' => 'Notifications',
                'icon' => 'FaRegBell',
                'url' => route('notifications.index'),
                'bg' => 'bg-green-700',
                'textColor' => 'text-white',
                'routeForActive' => 'notifications*',
                'requiredPermission' => 'notifications.view',

                'type' => 2,
                'order' => 1,
            ],
            [
                'name' => 'Settings',
                'icon' => 'RiSettings3Line',
                'url' => route('settings.index'),
                'bg' => 'bg-green-700',
                'textColor' => 'text-white',
                'routeForActive' => 'settings*',
                'requiredPermission' => 'settings.view',

                'type' => 2,
                'order' => 2,
            ],
            [
                'name' => 'Settings',
                'icon' => 'TbClockBolt',
                'url' => '#',
                'bg' => 'bg-green-700',
                'textColor' => 'text-white',
                'routeForActive' => 'settings*',
                'requiredPermission' => 'settings.view',

                'type' => 2,
                'order' => 3,
            ],
        ])->sortBy('order');



       $filteredData = $data->map(function ($item) {
            if (request()->user()->can($item['requiredPermission'])) {
                return $item;
            }
            return null;
        })->filter()->toBase()->values();

    
        return response()->json($filteredData);
    }


    public function sideMenu()
    {
        $sidebarItems = collect([
            [
                'name' => 'Dashboard',
                'icon' => 'MdOutlineDashboard',
                'url' => route('dashboard'),
                'routeForActive' => 'dashboard',
                'order' => 1,
                'requiredPermission' => 'dashboard.view',
            ],
            [
                'name' => 'Leads & Customers',
                'icon' => 'HiOutlineUsers',
                'url' => route('leadscustomers.index'),
                'routeForActive' => 'leadscustomers*',
                'order' => 2,
                'requiredPermission' => 'leadscustomers.view',
            ],
            [
                'name' => 'Billing',
                'icon' => 'IoReceiptOutline',
                'url' => route('billing.index'),
                'routeForActive' => 'billing*',
                'order' => 3,
                'requiredPermission' => 'billing.view',
            ],
            [
                'name' => 'Subscription',
                'icon' => 'TbCoinRupee',
                'url' => route('subscriptions.index'),
                'routeForActive' => 'subscriptions*',
                'order' => 4,
                'requiredPermission' => 'subscriptions.view',
            ],
            [
                'name' => 'Projects',
                'icon' => 'MdOutlineFolderCopy',
                'url' => route('projects.index'),

                'routeForActive' => 'projects*',
                'order' => 5,
                'requiredPermission' => 'projects.view',
            ],
            [
                'name' => 'Admin',
                'icon' => 'FaMoneyCheckDollar',
                'url' => route('admin.index'),

                'routeForActive' => 'admin*',
                'order' => 6,
                'requiredPermission' => 'admin.view',
            ],
            [
                'name' => 'Products',
                'icon' => 'MdOutlineCategory',
                'url' => route('products.index'),
                'routeForActive' => 'products*',

                'order' => 7,
                'requiredPermission' => 'products.view',
            ],
            [
                'name' => 'Integration',
                'icon' => 'FaNetworkWired',
                'url' => route('integration.call.index'),

                'routeForActive' => 'integration*',
                'order' => 8,
                'requiredPermission' => 'integration.view',
            ],
        ])->sortBy('order');

        $filteredData = $sidebarItems->map(function ($item) {
            if (request()->user()->can($item['requiredPermission'])) {
                return $item;
            }
            return null;
        })->filter()->toBase()->values();
        return response()->json($filteredData);
    }
}
