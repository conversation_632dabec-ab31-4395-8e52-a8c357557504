<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WaBot;
use App\Models\WaBotKeyword;
use App\Models\WaBotKeywordMsg;
use App\Models\WaBotWelcomeMsg;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class BotController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBot())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tableName = $this->tableName;
        if (!request()->user()->can("$tableName.view")) {
            abort(403, "unauthorized");
        }

        try {
            $request = request();
            $data['getData'] = $request->all();
            
            // Get pagination and sorting parameters
            $column = $request->query('column', 'id');
            $sortBy = $request->query('sort', 'desc');
            $search = $request->query('search', '');

            // Set permissions for main table
            $user = $request->user();
            $data = array_merge($data, [
                'can_add' => $user->can("$tableName.add"),
                'can_edit' => $user->can("$tableName.edit"),
                'can_delete' => $user->can("$tableName.delete")
            ]);

            // Define models and set their permissions
            $models = [
                'keyword' => WaBotKeyword::class,
                'keywordMessage' => WaBotKeywordMsg::class,
                'welcomeMessage' => WaBotWelcomeMsg::class,
            ];

            // Set permissions for each model in a single loop
            foreach ($models as $key => $model) {
                $table = (new $model())->getTable();
                $data["can_$key"] = [
                    'add' => $user->can("$table.add"),
                    'edit' => $user->can("$table.edit"),
                    'view' => $user->can("$table.view"),
                    'delete' => $user->can("$table.delete")
                ];
            }

            $getUser = request()->query("userID");
            $query = WaBot::orderBy($column, $sortBy)->accessibleByUser(["user_id"=>$getUser]);
         
            $query->when($search, function ($query, $search) {
                return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('id', 'like', '%' . $search . '%');
            });

            if (request()->header('Accept') != 'application/json') {
                $query->withCount("Message", "rules")
                    ->with('user', "rules");
            }
            // $data["bots"] = $query->paginate($totalCount)->withQueryString();


            $data['bots'] = $request->query('all', false)
                ? $query->get()
                : $query->paginate($request->query('perPage', 10))->withQueryString();



            return (request()->header('Accept') == 'application/json') ?
                response()->json(['collection' => $data, "getData" => $_GET, 'status' => true])
                :
                Inertia::render('WhatsApp/Bot/Index', ["collection" => $data]);

            // if (request()->header('Accept') == 'application/json') {
            //     $data = $query->paginate($totalCount)->withQueryString();

            //     return response()->json(['collection' => $data, "getData" => $_GET, 'status' => true]);
            // } else {
            //     $data["bots"] = $query->withCount("Message", "rules")
            //         ->with('user', "rules")
            //         ->paginate($totalCount)->withQueryString();
            //     return Inertia::render('WhatsApp/Bot/Index', ["collection" => $data]);
            // }
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        return Inertia::render('WhatsApp/Bot/Add');
    }
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required|max:45",
            "enableWelcome" => "required",

        ]);

        try {
            $bot = new WaBot();
            $bot->name = $request->name;
            $bot->user_id = Auth::id();
            $bot->save();
            if ($request->enableWelcome == 1) {

                $validator = Validator::make(
                    $request->all(),
                    [
                        'message' => 'bail|required_without:attachment',
                        'attachment.*' => 'required_without:message|file|max:20000',
                    ],
                    [
                        "attachment.required_without" => "Please ensure that you provide either a message or a file upload, or both.",
                        "message.required_without" => "Please ensure that you provide either a message or a file upload, or both.",
                    ]
                );
                if ($validator->fails()) {
                    return (request()->header('Accept') == 'application/json')
                        ? response()->json(['message' => 'Unable to add Bot', 'errors' => $validator->errors(), 'status' => false])
                        : redirect()->back()->withErrors($validator->errors());
                }
                $bot->save();
                $camp_msg = new WaBotWelcomeMsg();
                $camp_msg->body = $request->message;
                $camp_msg->wa_bot_id = $bot->id;

                if ($request->hasFile('attachment')) {
                    foreach ($request->attachment as $file) {
                        $fileName = time() . '-' . $file->getClientOriginalName();
                        $path = storeFile('uploads/' . $this->tableName, $file, $fileName);
                        $camp_msg->file = $path;
                    }
                }
                $camp_msg->save();
                return (request()->header('Accept') == 'application/json')
                    ? response()->json(['message' => "Bot Created", 'status' => true])
                    : redirect(route('whatsapp.bot.index'))->with(['message' => "Bot Created", 'type' => "success"]);
            }
            $bot->save();
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => "Bot Created", 'status' => true])
                : redirect(route('whatsapp.bot.index'))->with(['message' => "Bot Created", 'type' => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (!request()->user()->can($this->tableName . '.view')) {
            abort(403, "unauthorized");
        }
        try {
            $bot = WaBot::where('user_id', Auth::id())->with('rules', 'Message')->find($id);
            if ($bot == null) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Record not found', 'status' => false]) :
                    redirect(route('whatsapp.bot.index'))->with(['message' => 'Record not found', 'type' => 'failure']);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Record found', 'status' => true, 'data' => $bot]) :
                Inertia::render('WhatsApp/Bot/Show', ['collection' => $bot]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }

        try {
            $bot = WaBot::with('rules', 'Message')->find($id);
            if (!$bot) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Record not found', 'status' => false]) :
                    redirect(route('whatsapp.bot.index'))->with(['message' => 'Record not found', 'type' => 'failure']);
            }

            if ($bot->user_id == Auth::id() || $bot->user->parentUser_id == Auth::id()) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Record found', 'status' => true, 'data' => $bot]) :
                    Inertia::render('WhatsApp/Bot/Edit', ['collection' => $bot]);
            }

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect(route('whatsapp.bot.index'))->with(['message' => 'Unauthorized', 'type' => 'failure']);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required|max:45",
        ]);
        try {
            $bot = WaBot::find($id);
            if ($bot == null) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Bot not Found.", 'status' => false]) :
                    redirect(route('whatsapp.bot.index'))->with(["message" => "Bot not Found.", "type" => "failure"]);
            }
            if ($bot->user_id == Auth::id() || $bot->user->parentUser_id == Auth::id()) {
                $bot->name = $request->name;
                $bot->update();

                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Bot Updated", 'status' => true]) :
                    redirect(route('whatsapp.bot.index'))->with(["message" => "Bot Updated", "type" => "success"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect(route('whatsapp.bot.index'))->with(['message' => 'Unauthorized', 'type' => 'failure']);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }

        try {
            $ids = explode(",", $id);
            $waBots = WaBot::whereIn('id', $ids);
            if (request()->user()->parentUser_id) {
                $waBots->whereIn('user_id', function ($query) {
                    $query->select('id')
                        ->from((new User())->getTable())
                        ->whereRaw('id = ?', [Auth::id()])
                        ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
                });
            }
            $waBots->get();
            if ($waBots->count()) {
                $waBots = WaBot::whereIn('id', $ids);
                if (request()->user()->parentUser_id) {
                    $waBots->whereIn('user_id', function ($query) {
                        $query->select('id')
                            ->from((new User())->getTable())
                            ->whereRaw('id = ?', [Auth::id()])
                            ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
                    });
                }
                $waBots->delete();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Record(s) Deleted", 'status' => true]) :
                    redirect()->back()->with(["message" => "Record(s) Deleted", "type" => "success"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Records not found", 'status' => false]) :
                redirect()->back()->with(["message" => "Record(s) Deleted !!", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
}
