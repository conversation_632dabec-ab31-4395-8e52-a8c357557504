<?php

namespace App\Http\Controllers\Mail;

use App\Http\Controllers\Controller;
use App\Models\Mail\Gateway;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class GatewayController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new Gateway())->getTable();
    }
    /**
     * Display a listing of the resource.
     */

    public function index()
    {
        $tableName = $this->tableName;
        if (!request()->user()->can("$tableName.index")) {
            abort(403, "unauthorized");
        }
        try {
            $request = request();
            $data = [
                'getData' => $request->query(),
                'gateways' => Gateway::query()
                    ->orderBy($request->query('column', 'id'), $request->query('sort', 'desc'))
                    ->when($request->query('userID'), function ($query) use ($request) {
                        $user = User::find($request->query('userID'));
                        $userIds = $request->user()->getAllChildUserIds($request->query('userID'));
                        return $request->user()->isAdmin() || in_array($user->id, $userIds->toArray())
                            ? $query->whereIn('user_id', $userIds)
                            : $query->where('id', 0); // Return empty result if not authorized
                    }, function ($query) use ($request) {
                        return $query->whereIn('user_id', $request->user()->getAllChildUserIds());
                    })
                    ->when($request->query('status'), function ($query, $status) {
                        return $query->where('status', $status);
                    })
                    ->with('user:id,username')
                    ->when($request->query('search'), function ($query, $search) {
                        return $query->whereAny(['name', 'id'], 'like', "%$search%");
                    })
            ];

            // dd($data['gateways']->getModel()->getTable());
            $data['gateways'] = $request->query('all', false)
                ? $data['gateways']->get()
                : $data['gateways']->paginate($request->query('perPage', 10))->withQueryString();
            $data += [
                "can_add" => request()->user()->can("$tableName.add"),
                "can_edit" => request()->user()->can("$tableName.edit"),
                "can_delete" => request()->user()->can("$tableName.delete"),
                "can_view" => request()->user()->can("$tableName.view")
            ];
            return $request->header('Accept') == 'application/json'
                ? response()->json(['message' => "Gateway List.", 'status' => true, 'collection' => $data])
                : Inertia::render('Mail/Gateways/Index', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can("$this->tableName.add")) {
            abort(403, "unauthorized");
        }

        $rules = [
            'name' => "required|string|max:100|unique:{$this->tableName},name",
            'username' => 'required|string|max:250',
            'port' => 'required|numeric',
            'password' => 'required|string|min:8',
            'limit' => 'required|numeric',
            'host' => 'required|string|max:100',
        ];

        $request->validate($rules, [], [
            'username' => 'Username',
            'password' => 'Password',
            'name' => 'Name',

            'host' => 'Host',
            'port' => 'Port',
            'email_from' => 'Email From',
            'limit' => 'Limit',
        ]);

        try {
            if ($request->has('id')) {
                $gateway = Gateway::find($request->id);
            } else {
                $gateway = new Gateway();
            }
            $gateway->name = $request->name;
            $gateway->username = $request->username;
            $gateway->password = $request->password;
            $gateway->host = $request->host;
            $gateway->port = $request->port;
            $gateway->limit_per_minute = $request->limit;
            $gateway->user_id = Auth::id();
            $gateway->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Gateway Created.", "status" => true])
                : redirect()->back()->with(["message" => "Gateway Created.", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (!request()->user()->can("$this->tableName.edit")) {
            abort(403, "unauthorized");
        }
        $request->validate(
            [
                'name' => "required|string|max:100|unique:{$this->tableName},name,{$id}",
                'username' => 'required|string|max:250',
                'port' => 'required|numeric',
                'password' => 'required|string|min:8',
                'limit' => 'required|numeric',
                'host' => 'required|string|max:100',
            ],
            [],
            [
                'username' => 'Username',
                'password' => 'Password',
                'name' => 'Name',
                'host' => 'Host',
                'port' => 'Port',
                'limit' => 'Limit',
            ]
        );

        try {
            $gateway = Gateway::find($request->id);
            $gateway->username = $request->username;
            $gateway->password = $request->password;
            $gateway->name = $request->name;
            $gateway->host = $request->host;
            $gateway->port = $request->port;
            $gateway->limit_per_minute = $request->limit;
            $gateway->user_id = Auth::id();
            $gateway->update();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Gateway Updated.", "status" => true])
                : redirect()->back()->with(["message" => "Gateway Updated.", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "status" => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can("$this->tableName.delete")) {
            abort(403, "unauthorized");
        }

        try {
            $ids = explode(",", $id);
            $gateways = Gateway::whereIn('id', $ids)->accessibleByUser();

            $status = $gateways->exists();
            $message = $status ? "Gateway deleted." : "Gateway Not Found.";

            if ($status) {
                $gateways->delete();
            }

            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => $message, 'status' => $status])
                : redirect()->back()->with([
                    "message" => $message,
                    "type" => $status ? "success" : "failure"
                ]);
        } catch (Exception $e) {
            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => $message, 'status' => false])
                : redirect()->back()->with(["message" => $message, "type" => "failure"]);
        }
    }
}
