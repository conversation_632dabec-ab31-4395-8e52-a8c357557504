<?php

namespace App\Http\Controllers\Voice;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class VoiceController extends Controller
{
    public function dashboard()
    {
        
        return Inertia::render('Voice/Dashboard');
    }
    public function campaign()
    {
        
        return Inertia::render('Voice/Campaigns/Index');
    }
    // mails functions
    public function mails()
    {
        
        return Inertia::render('Voice/Mails/Index');
    }
    public function compose()
    {
        return Inertia::render('Voice/Mails/Compose');
    }
    public function mailsInbox()
    {
        return Inertia::render('Voice/Mails/Inbox');
    }
    public function mailsSent()
    {
        return Inertia::render('Voice/Mails/Sent');
    }
    public function mailsJunk()
    {
        return Inertia::render('Voice/Mails/Junk');
    }
    public function mailsArchive()
    {
        return Inertia::render('Voice/Mails/Archive');
    }
    public function gateways()
    {
        return Inertia::render('Voice/Gateways/Index');
    }
    public function bots()
    {
        
        return Inertia::render('Voice/Bots');
    }

    public function templates()
    {
        return Inertia::render('Voice/Templates/Index');
    }

    public function contacts()
    {
        return Inertia::render('Voice/Contacts');
    }

    public function notInCRM()
    {
        return Inertia::render('Voice/NotInCRM/Index');
    }

    public function unapproved()
    {
        return Inertia::render('Voice/Unapproved/Index');
    }

    public function openMail()
    {
        return Inertia::render('Voice/Mails/OpenMail');
    }
    public function viewMail()
    {
        return Inertia::render('Voice/Mails/ViewMail');
    }
}
