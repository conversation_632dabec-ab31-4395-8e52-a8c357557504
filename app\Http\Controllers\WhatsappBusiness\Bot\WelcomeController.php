<?php

namespace App\Http\Controllers\WhatsappBusiness\Bot;

// namespace App\Http\Controllers\Whatsapp;

use Exception;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\WhatsappBusiness\bot\WaBaBot;
use App\Models\WhatsappBusiness\bot\WaBaBotWelcomeMsg;

class WelcomeController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBaBotWelcomeMsg())->getTable();
    }
    /**
     * Display a listing of the resource.
     */

    public function index($bot)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        try {
            $botData = $this->isMyBot($bot);
            if (!$botData) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Unauthorized', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
            }
            $search = request()->query('search', null);
            $perPage = request()->query('perPage', 10);
            $column = request()->query('sortColumn', 'id');
            $direction = request()->query('sortBy', 'desc');
            $data['getData'] = $_GET;
            $data['messages'] = WaBaBotWelcomeMsg::orderBy($column, $direction)
                ->where('waba_bot_id', $bot)
                ->when($search, function ($query, $search) {
                    return $query->whereAny(['body'], 'LIKE', "%{$search}%");
                })
                ->paginate($perPage)->withQueryString();

            $data["bot"] = $botData;
            $data["can_add"] = request()->user()->can($this->tableName . ".add");
            $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
            $data["can_delete"] = request()->user()->can($this->tableName . ".delete");

            

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'data' => $data, 'message' => 'Messages fetched successfully']) :
                Inertia::render('WhatsappBusiness/Bot/WelcomeMessage/Index', ["collection" => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create() {}

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $bot)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        $request->validate(
            [
                'message' => 'bail|required_without:attachment',
                'attachment.*' => 'required_without:message|file|max:20000',
            ],
            [],
            ['attachment' => 'File', 'message' => 'Message', 'attachment.*' => 'File']
        );

        try {
            $message = new WaBaBotWelcomeMsg();
            $message->body = $request->message;
            if ($request->attachment && count($request->attachment) > 0) {

                foreach ($request->attachment as $key => $value) {
                    if (isset($value)) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = storeFile('uploads/' . $this->tableName, $value, $fileName);
                        $message->file = $path ?? "";
                    }
                }
            }
            $message->waba_bot_id = $bot;
            $message->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Message created successfully']) :
                redirect()->back()->with(["message" => 'Message created successfully', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($bot, string $id)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        try {

            $message = WaBaBotWelcomeMsg::where(['waba_bot_id' => $bot, 'id' => $id])->first();
            if (empty($message)) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Message not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Message not found', "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'data' => $message]) :
                view('whatsapp.bot-welcome.show', compact('message'));
        } catch (\Throwable $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id, $bot)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
        try {
            $botData = $this->isMyBot($bot);
            if (!$botData) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Unauthorized', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
            }

            $message = WaBaBotWelcomeMsg::where(['waba_bot_id' => $bot, 'id' => $id])->first();
            if (empty($message)) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Message not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Message not found', "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'data' => $message]) :
                view('whatsapp.bot-welcome.show', compact('message'));
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }

        $request->validate(
            [
                'message' => 'bail|required_without:attachment',
                'attachment.*' => 'required_without:message|file|max:20000',
            ],
            [],
            ['attachment' => 'File', 'message' => 'Message', 'attachment.*' => 'File']
        );
        try {
            $message = WaBaBotWelcomeMsg::find($id);
            if (empty($message)) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Message not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Message not found', "type" => "failure"]);
            }

            $message->body = $request->message;

            if ($request->hasFile('attachment')) {
                deleteFile($message->file);
                foreach ($request->attachment as $file) {
                    $fileName = time() . '-' . $file->getClientOriginalName();
                    $path = storeFile('uploads/' . $this->tableName, $file, $fileName);
                    $message->file = $path;
                }
            }
            $message->update();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Welcome Message updated successfully']) :
                redirect()->back()->with(['type' => "success", "message" => 'Welcome Message updated successfully']);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($bot, string $id)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }

        try {
            $ids = explode(',', $id);
            $botsWelcome = WaBaBotWelcomeMsg::whereIn('id', $ids)->where('waba_bot_id', $bot)->get();
            if ($botsWelcome->isEmpty()) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Message not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Message not found', "type" => "failure"]);
            }
            WaBaBotWelcomeMsg::whereIn('id', $ids)->where(['waba_bot_id' => $bot])->delete();
            foreach ($botsWelcome as $bot) {
                deleteFile($bot->file);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Message deleted successfully']) :
                redirect()->back()->with(["type" => 'success', "message" => 'Message deleted successfully']);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function isMyBot($bot)
    {
        $bot = WaBaBot::where(['user_id' => Auth::id(), 'id' => $bot])->first();
        if ($bot) {
            return $bot;
        } else {
            return false;
        }
    }
}
