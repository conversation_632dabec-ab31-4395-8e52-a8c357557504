import { FilePreview } from "@/Components/FilePreview";
import MessageBox from "@/Components/HelperComponents/MessageBox";
import TemplateBox from "@/Components/TemplateBox";
import useFetch from "@/Global/useFetch";
import {
    input_custom
} from "@/Pages/Helpers/DesignHelper";
import { textFormatting } from "@/Pages/Helpers/Helper";
import { router, useForm } from "@inertiajs/react";
import {
    Button,
    Label,
    Select,
    TextInput
} from "flowbite-react";
import { useEffect, useState } from "react";

export default function Add({ onClose, setTemplate, selectedCategory, setSelectedCategory }) {
    const [image, setImage] = useState("");
    const { data: fetchData, loading: fetchDataLoading, error } = useFetch(route('whatsapp.templates.category.index'));

    const { data, setData, post, processing, reset, errors } = useForm({
        msg: "",
        templateName: "",
        category_id: "",
        // file: [],
        files: [],

    });

    useEffect(() => {
        setData('message', data.msg);
    }, [data.msg]);


    useEffect(() => {
        setData('attachment', data.files);
        setImage(data.files[0])
    }, [data.files]);


    function handleTemplateForm(e) {
        e.preventDefault();
        post(route("whatsapp.templates.store"), {
            onSuccess: () => {
                reset();
                onClose(true);
            },
        });
    }

    return (
        <form onSubmit={handleTemplateForm}>
            <div className="">
                <div className="grid items-start grid-flow-row-dense grid-cols-12 gap-3">
                    <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-8 lg:col-span-8 md:col-span-12 col-span-full">
                        <div className="flex gap-3 p-2 px-3 bg-white rounded-lg ">
                            <div className="w-full">
                                <div className="block mb-2">
                                    <Label
                                        htmlFor="templateName"

                                    >Template Name<small className="text-red-600">*</small>
                                    </Label>
                                </div>
                                <TextInput
                                    theme={input_custom}
                                    id="templateName"
                                    type="text"
                                    value={data.templateName}
                                    onChange={(e) =>
                                        setData("templateName", e.target.value)
                                    }
                                    placeholder="E.g. Welcome New Customers"
                                    color={
                                        errors.templateName ? "failure" : "gray"
                                    }
                                    required
                                    helperText={
                                        errors.templateName &&
                                        errors.templateName
                                    }
                                />
                            </div>
                            <div className="w-full">
                                <div className="block mb-2">
                                    <Label
                                        htmlFor="categories"
                                    >        Category<small className="text-red-600">*</small>
                                    </Label>
                                </div>

                                <Select
                                    id="categories"
                                    required
                                    onChange={(e) => {
                                        setData("category_id", e.target.value);
                                    }}
                                    color={
                                        errors.category_id ? "failure" : "gray"
                                    }
                                    helperText={
                                        errors.category_id && errors.category_id
                                    }

                                >
                                    <option disabled selected value={""}>
                                        Select Category
                                    </option>

                                    {!fetchDataLoading && fetchData?.collection?.categories.map((category, key) => {
                                        return (
                                            <option value={category.id} key={'Select-Category-' + key}>
                                                {category.name}
                                            </option>
                                        );
                                    })}
                                </Select>
                            </div>
                        </div>
                        <div className="p-2 bg-white rounded-md">
                            <div className="mb-2 text-sm font-medium">
                                Compose Message
                            </div>
                            <MessageBox data={data.msg} fileUpload={true}
                                setData={setData} allData={data}
                                errors={errors}
                                isOfficial
                            />
                        </div>
                        <div className="flex justify-end mt-3">
                            <Button color="blue" className="rounded" type="submit" isProcessing={processing}>

                                Submit
                            </Button>
                        </div>
                    </div>
                    <div className="col-span-12 xl:col-span-4 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex">
                        <div className="w-full m-auto">
                            <TemplateBox>
                                <FilePreview object={image} imageSize="xl" />
                                {data.msg && (
                                    <div className="break-all word-break"
                                        dangerouslySetInnerHTML={{__html: textFormatting(data.msg)}}
                                    />
                                )}
                                <div className="text-xs text-end text-slate-700">9:45</div>
                            </TemplateBox>

                        </div>
                    </div>
                </div>
            </div>
        </form>
    );
}
