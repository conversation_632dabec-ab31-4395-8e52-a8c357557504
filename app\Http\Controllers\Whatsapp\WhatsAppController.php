<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\ContactList;
use App\Models\WaBot;
use App\Models\WaCampaign;
use App\Models\WaChannel;
use App\Models\WaTemplate;
use App\Models\WaTemplateView;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use PDOException;

class WhatsAppController extends Controller
{
    /**
     * Display a listing of the resource.
     * 0 - Draft
     * 1 - Started
     * 2 - Paused
     * 3 - Completed
     * 4 - Terminated
     */

    public function index()
    {
        $userId = Auth::id();
        $campaigns =  WaCampaign::where('user_id', $userId)->get();
        $data['totalCampaign'] = $campaigns->count();
        $data['draft'] = $campaigns->where('status', 0)->count();
        $data['started'] = $campaigns->where('status', 1)->count();
        $data['scheduled'] = $campaigns->where('sendAfterDateTime', ">", now())->count();
        $data['paused'] = $campaigns->where('status', 2)->count();
        $data['completed'] = $campaigns->where('status', 3)->count();
        $data['terminated'] = $campaigns->where('status', 4)->count();
        return Inertia::render('WhatsApp/Dashboard', ['collection' => $data]);
    }


    public function menuItems()
    {
        $data = [];
        try {
            $wa_user_counts = DB::table('wa_counts')->where('user_id', Auth::id())->first();
            $data["campaigns"] = $wa_user_counts->campaign;
            $data["bots"] = $wa_user_counts->bots;
            $data["templates"] = $wa_user_counts->template;
            $data["gateways"] = $wa_user_counts->gateway;
        } catch (Exception $e) {
          
        }
        return response()->json($data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
       
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
