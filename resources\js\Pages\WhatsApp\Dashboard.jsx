import { Link } from "@inertiajs/react";
import { But<PERSON> } from "flowbite-react";
import { MdOutlineRocketLaunch } from "react-icons/md";
import { button_custom } from "../Helpers/DesignHelper";
import { showAsset } from "../Helpers/Helper";
import WaMain from "./WaMain";

export default function Dashboard({ collection }) {

    const CampaignGridItems = [
        {
            name: 'Campaigns',
            value: collection.totalCampaign,
            image: showAsset("/assets/img/campaigns.png", ""),
        },
        {
            name: 'Started',
            value: collection.started,
            image: showAsset("/assets/img/started.png", ""),
        },
        {
            name: 'Scheduled',
            value: collection.scheduled,
            image: showAsset("/assets/img/scheduled.png", ""),
        },
        {
            name: 'Terminated',
            value: collection.terminated,
            image: showAsset("/assets/img/terminated.png", ""),
        },
        {
            name: 'Paused',
            value: collection.paused,
            image: showAsset("/assets/img/paused.png", ""),
        },
        {
            name: 'Completed',
            value: collection.completed,
            image: showAsset("/assets/img/completed.png", ""),
        },
    ]

    return (
        <WaMain>
            
            <div className="px-2 py-3 ">
                <div className="mb-3">
                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                        <div className="col-span-12 bg-blue-600 xl:col-span-3 lg:col-span-3 md:col-span-12 ">
                            {/* <div className=""> */}
                            <div className="h-full p-2">
                                <h4 className="text-lg text-white">
                                    Track Performance
                                </h4>
                                <p className="mb-2 text-sm text-gray-100 ">
                                    Analyze delivery rates, engagement, and
                                    responses.
                                </p>
                                <Button
                                    className="w-fit"
                                    theme={button_custom}
                                    color="green"
                                    size="xs"
                                    as={Link}
                                    href={route('whatsapp.campaign.index')}
                                >
                                    <div className="flex items-center gap-1 text-sm">
                                        <MdOutlineRocketLaunch className="text-lg" />
                                        Launch Campaign
                                    </div>
                                </Button>
                            </div>
                        </div>
                        <div className="h-full col-span-12 rounded-lg xl:col-span-9 lg:col-span-9 md:col-span-12">
                            <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1">
                                {CampaignGridItems.map((item, k) => {
                                    return (
                                        <div key={'itemGrid-' + k} className="h-full col-span-12 p-2 bg-white border rounded-lg lg:col-span-4 md:col-span-6">
                                            <div className="flex items-start justify-between">
                                                <div className="flex flex-col gap-2">
                                                    <span className="text-lg">
                                                        {item.name}
                                                    </span>
                                                    <div className="text-xl font-medium">

                                                        {item.value}
                                                    </div>
                                                </div>
                                                <img
                                                    src={item.image}
                                                    alt=""
                                                />
                                            </div>
                                        </div>
                                    )
                                })}

                            </div>
                        </div>
                    </div>
                </div>

                {/* <div className="h-full col-span-12 p-2 bg-white border rounded lg:col-span-4 md:col-span-12">
                    <div className="flex items-start justify-between">
                        <div className="flex flex-col gap-2">

                            <div className="h-full col-span-12 p-1 bg-white rounded xl:col-span-4 lg:col-span-6 md:col-span-6">
                                <div className="text-lg font-medium">
                                    Performance Overview
                                </div>
                                <div className="m-1 border rounded">
                                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-b md:grid-cols-12 sm:grid-cols-1">
                                        <div className="h-full col-span-12 border-r lg:col-span-6 md:col-span-12">
                                            <div className="flex items-center gap-2 p-2">
                                                <img
                                                    className="size-9"
                                                    src={showAsset(
                                                        "/assets/img/totalmsgs.png", ""
                                                    )}
                                                    alt=""
                                                />
                                                <div className="flex flex-col">
                                                    <div className="text-base font-medium">
                                                        Total Messages
                                                    </div>
                                                    <span className="text-sm text-gray-500">
                                                        80,525
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="h-full col-span-12 lg:col-span-6 md:col-span-12">
                                            <div className="flex items-center gap-2 p-2">
                                                <img
                                                    className="size-9"
                                                    src={showAsset(
                                                        "/assets/img/bots.png", ""
                                                    )}
                                                    alt=""
                                                />
                                                <div className="flex flex-col w-full">
                                                    <div className="flex items-center justify-between text-base font-medium">
                                                        <span>Bots</span>
                                                        <IoAddOutline className="text-gray-500" />
                                                    </div>
                                                    <span className="text-sm text-gray-500">
                                                        12 Rules & 7 Keywords
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 md:grid-cols-12 sm:grid-cols-1">
                                        <div className="h-full col-span-12 border-r lg:col-span-6 md:col-span-12">
                                            <div className="flex items-center gap-2 p-2">
                                                <img
                                                    className="size-9"
                                                    src={showAsset(
                                                        "/assets/img/gateway.png", ""
                                                    )}
                                                    alt=""
                                                />
                                                <div className="flex flex-col w-full">
                                                    <div className="flex items-center justify-between text-base font-medium">
                                                        <span>Gateways</span>
                                                        <IoAddOutline className="text-gray-500" />
                                                    </div>
                                                    <span className="text-sm text-gray-500">
                                                        12 Open out of 15
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="h-full col-span-12 lg:col-span-6 md:col-span-12">
                                            <div className="flex items-center gap-2 p-2">
                                                <img
                                                    className="size-9"
                                                    src={showAsset(
                                                        "/assets/img/contacts.png", ""
                                                    )}
                                                    alt=""
                                                />
                                                <div className="flex flex-col w-full">
                                                    <div className="flex items-center justify-between text-base font-medium">
                                                        <span>Contacts</span>
                                                        <IoAddOutline className="text-gray-500" />
                                                    </div>
                                                    <span className="text-sm text-gray-500">
                                                        10,525 in 5 Lists
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 md:grid-cols-12 sm:grid-cols-1">
                                        <div className="h-full col-span-12 lg:col-span-6 md:col-span-12"></div>
                                        <div className="h-full col-span-12 lg:col-span-6 md:col-span-12"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> */}
            </div>
        </WaMain>
    );
}
