import { useState, useMemo, useCallback } from 'react';

/**
 * Custom hook for managing template state and filtering
 * Prevents unnecessary re-renders and optimizes performance
 */
export const useTemplateState = (initialTemplates) => {
    const [selectedCategory, setSelectedCategory] = useState("0");
    const [modalState, setModalState] = useState({ 
        openAdd: false, 
        openEdit: false, 
        openView: false, 
        selected: null 
    });
    const [selectedTemplates, setSelectedTemplates] = useState([]);
    const [isMultipleDeleteConfirmOpen, setIsMultipleDeleteConfirmOpen] = useState(false);

    // Memoized filtered templates based on selected category
    const filteredTemplates = useMemo(() => {
        if (!initialTemplates?.data) return { ...initialTemplates, data: [] };
        
        if (selectedCategory === "0" || selectedCategory === 0) {
            return initialTemplates; // Show all templates
        }
        
        const filtered = initialTemplates.data.filter(template => 
            template.mail_template_category_id == selectedCategory
        );
        
        return {
            ...initialTemplates,
            data: filtered
        };
    }, [initialTemplates, selectedCategory]);

    // Optimized category selection handler
    const handleCategorySelect = useCallback((categoryId) => {
        setSelectedCategory(categoryId);
    }, []);

    // Optimized modal state handlers
    const handleModalStateChange = useCallback((newState) => {
        setModalState(newState);
    }, []);

    const handleTemplateView = useCallback((template) => {
        setModalState({ openView: true, selected: template });
    }, []);

    const handleTemplateEdit = useCallback((template) => {
        setModalState({ openEdit: true, selected: template });
    }, []);

    const handleTemplateDelete = useCallback((template) => {
        setIsMultipleDeleteConfirmOpen(true);
        setSelectedTemplates([template.id]);
    }, []);

    // Modal close handlers
    const closeAddModal = useCallback(() => {
        setModalState(prev => ({ ...prev, openAdd: false }));
    }, []);

    const closeEditModal = useCallback(() => {
        setModalState(prev => ({ ...prev, openEdit: false }));
    }, []);

    const closeViewModal = useCallback(() => {
        setModalState(prev => ({ ...prev, openView: false }));
    }, []);

    const openAddModal = useCallback(() => {
        setModalState(prev => ({ ...prev, openAdd: true }));
    }, []);

    // Delete confirmation handlers
    const handleMultipleDelete = useCallback((confirmed) => {
        if (confirmed && selectedTemplates.length > 0) {
            // The actual delete logic will be handled by the parent component
            return selectedTemplates;
        }
        setSelectedTemplates([]);
        setIsMultipleDeleteConfirmOpen(false);
        return null;
    }, [selectedTemplates]);

    const closeDeleteConfirm = useCallback(() => {
        setSelectedTemplates([]);
        setIsMultipleDeleteConfirmOpen(false);
    }, []);

    return {
        // State
        selectedCategory,
        modalState,
        selectedTemplates,
        isMultipleDeleteConfirmOpen,
        filteredTemplates,
        
        // Handlers
        handleCategorySelect,
        handleModalStateChange,
        handleTemplateView,
        handleTemplateEdit,
        handleTemplateDelete,
        handleMultipleDelete,
        closeAddModal,
        closeEditModal,
        closeViewModal,
        openAddModal,
        closeDeleteConfirm,
        
        // Setters (for backward compatibility)
        setSelectedCategory,
        setModalState,
        setSelectedTemplates,
        setIsMultipleDeleteConfirmOpen
    };
};
