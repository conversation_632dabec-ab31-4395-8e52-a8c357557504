<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\ContactList;
use App\Models\User;
use App\Models\WaBulkCampaignChannel;
use App\Models\WaBulkCampaignContactList;
use App\Models\WaBulkCampaignMsg;
use App\Models\WaCampaign;
use App\Models\WaChannel;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use PDOException;

class CampaignController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaCampaign())->getTable();
    }

    /**
     * Display a listing of the resource.
     */

    public function index()
    {

        
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }
        try {
            $totalCount = request()->query('perPage', 10);
            $column = request()->query('column', 'id');
            $sortBy = request()->query('sort', 'desc');


            $getUser = request()->query("userID");
            $query = WaCampaign::orderBy($column, $sortBy)->accessibleByUser(["user_id"=>$getUser]);
            $backData["campaigns"] = $query->with('channels', 'contactLists', 'user:id,username')
                ->paginate($totalCount)
                ->withQueryString()
                ->toArray();
            $backData['getData'] = request()->query();
            $backData["can_add"] = request()->user()->can($this->tableName . ".add");
            $backData["can_edit"] = request()->user()->can($this->tableName . ".edit");
            $backData["can_delete"] = request()->user()->can($this->tableName . ".delete");

            return request()->header('Accept') == 'application/json'
                ? response()->json(['status' => true, 'collection' => $backData])
                : Inertia::render('WhatsApp/Campaign/Index', ['collection' => $backData]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */

    public function create()
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        return Inertia::render('WhatsApp/Campaign/Steps/Add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        try {
            $request['channels'] = is_array($request->channels) ? $request->channels : json_decode($request->channels, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    "status" => false,
                    'message' => 'Invalid JSON in channels'
                ], 400);
            }

            $request['contactList'] = is_array($request->contactList) ? $request->contactList : json_decode($request->contactList, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    "status" => false,
                    'message' => 'Invalid JSON in contactList'
                ], 400);
            }
        } catch (Exception $e) {
            return response()->json([
                "status" => false,
                'message' => 'Invalid JSON in request.'
            ], 400);
        }

        $request->validate(
            [
                "campaignName" => "required|min:3|max:50",
                "channels" => "array|required",
                "contactList" => "array|required|max:5",
                "isFooterTimeEnable" => "required",
                "viewOnce" => "required",
                "startDate" => "required|date",
                "startTime" => "required",
                "enableEndTime" => "required",
                "stopDate" => "required_if:enableEndTime,true",
                "stopTime" => "required_if:enableEndTime,true",
                "sleepAfterMsgs" => "required|numeric|min:1",
                "sleepForSeconds" => "required|numeric|min:1",
            ],
            [],
            [
                "campaignName" => 'Campaign Name',
                "message" => 'Message',
                "isFooterTimeEnable" => 'Footer Time Enable',
                "viewOnce" => 'View Once',
                "sleepFor" => 'Sleep for',
                "sleepAfter" => 'Sleep After',
                "channels" => 'Channel',
                "startTime" => 'Start Time',
                "contactList" => 'Contact List',
                "mediaFiles" => 'Media',
            ],
        );
        DB::beginTransaction();
        try {
            $contactLists = ContactList::whereIn('id', $request->contactList)->withCount('contacts')->get();
            $Campaign = new WaCampaign();
            $Campaign->name = $request->campaignName;
            $Campaign->msgFooterDateTime = $request->isFooterTimeEnable;
            $Campaign->viewOnce = $request->viewOnce;
            $Campaign->sleepAfterMsgs = $request->sleepAfterMsgs;
            $Campaign->sleepForSeconds = $request->sleepForSeconds;
            $Campaign->user_id = Auth::id();
            if ($request->has(["startDate", "startTime"])) {
                $formateStopDateTime = (new DateTime("{$request->startDate} {$request->startTime}"))->format('Y-m-d H:i:s');
                $Campaign->startTime = $formateStopDateTime;
            }

            if ($request->enableEndTime == true) {
                if ($request->has(["stopDate", "stopTime"])) {
                    $formateStopDateTime = (new DateTime("{$request->stopDate} {$request->stopTime}"))->format('Y-m-d H:i:s');
                    $Campaign->endTime = $formateStopDateTime;
                }
            }

            $Campaign->totalContacts = $contactLists->sum('contacts_count'); // need contact list contacts

            $Campaign->save();
            $bulkContactListForCampaign = [];
            foreach ($request->contactList as $k => $cl) {
                $bulkContactListForCampaign[] = [
                    'wa_campaign_id' => $Campaign->id,
                    'contact_list_id' => $cl,
                    'user_id' => Auth::id(),
                ];
            }
            $bulkChannelsForCampaign = [];
            foreach ($request->channels as $ch) {
                $bulkChannelsForCampaign[] = [
                    'wa_campaign_id' => $Campaign->id,
                    'wa_gateway_id' => $ch,
                    'user_id' => Auth::id(),
                ];
            }
            $Campaign->channels()->createManyQuietly(
                $bulkChannelsForCampaign
            );
            $Campaign->contactLists()->createManyQuietly(
                $bulkContactListForCampaign
            );
            DB::commit();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign Created.", 'status' => true])
                :
                redirect()->route('whatsapp.campaign.create.message', ['campaign' => $Campaign->id]);
        } catch (PDOException $e) {
            DB::rollback();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (!request()->user()->can($this->tableName . '.view')) {
            abort(403, "unauthorized");
        }

        $userId = $_GET["userID"] ?? Auth::id();
        try {
            if (request()->header('Accept') == 'application/json') {
                $backData['campaign'] = WaCampaign::where('id', $id)
                    ->whereIn('user_id', function ($query) use ($userId) {
                        $query->select('id')
                            ->from((new User())->getTable())
                            ->whereRaw('id = ?', [$userId])
                            ->orWhereRaw('parentUser_id = ?', [$userId]);
                    })
                    ->with('channels', 'contactLists', 'user', 'messages')
                    ->withCount('failed', 'sent', 'nonWhatsapp')
                    ->first();
                if ($backData['campaign']) {
                    return response()->json(['data' => $backData, 'status' => true, 'message' => '']);
                }
                return response()->json(['data' => $backData, 'status' => true, 'message' => 'campaign not found.']);
            }

            $backData['campaign'] = WaCampaign::where('id', $id)
                ->whereIn('user_id', function ($query) use ($userId) {
                    $query->select('id')
                        ->from((new User())->getTable())
                        ->whereRaw('id = ?', [$userId])
                        ->orWhereRaw('parentUser_id = ?', [$userId]);
                })
                ->with('channels', 'contactLists', 'user', 'pivotChannels', 'messages')
                ->withCount('failed', 'sent', 'nonWhatsapp')
                ->first();
            $backData['pluckIds'] = $backData['campaign']->channels()->pluck('wa_gateway_id', 'wa_gateway_id');
            $backData['channels'] = WaChannel::where('user_id', Auth::id())->get();
            $backData['attachments'] = [];
            $backData['messages'] = $backData['campaign']->messages;
            $attachArray = explode(',', $backData['campaign']->wa_media);
            $backData['attachments_count'] = count($attachArray);
            // if ($backData['attachments_count'] > 0) {
            //     $backData['attachments'] = WaMedia::find($attachArray);
            // }
            $backData['id'] = $id;
            $allChannelIds = array_flip(array_column($backData["campaign"]->pivotChannels->toArray(), 'id'));
            $backData["remainingGateways"] = array_values(array_filter($backData["channels"]->toArray(), function ($record) use ($allChannelIds) {
                return !isset($allChannelIds[$record['id']]);
            }));
            return response()->json(['data' => $backData, 'status' => true]);
        } catch (Exception $e) {
            return response()->json(['message' => errorMessage(3), 'status' => false]);
            //    (request()->header('Accept') == 'application/json') ?
            // : redirect()->back()->with(["message" => errorMessage(3), "type" => "failure"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $campaign)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }

        try {
            $data['id'] = $campaign;
            $query = WaCampaign::where('id', $campaign);
            if (!request()->user()->isAdmin()) {
                $userIds = request()->user()->getAllChildUserIds();
                $query->whereIn('user_id', $userIds);
            }
            $query->with('pivotChannels', 'channels', "pivotContactLists", "messages");
            $data['campaign'] =  $query->first();

            if ($data['campaign'] == null) {
                return redirect(route('whatsapp.campaign.index'))->with(["message" => "Campaign not found.", "type" => "failure"]);
            }
            $data['channels'] = $data['campaign']->channels()->pluck('wa_gateway_id');
            $data['contactLists'] = $data['campaign']->contactLists()->pluck('contact_list_id');

            return (request()->header('Accept') == 'application/json')
                ? response()->json(['collection' => $data, 'status' => true])
                : Inertia::render('WhatsApp/Campaign/Steps/Edit', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Update the specified resource in storage.
     */

    public function update(Request $request, string $id)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }
        try {
            if (gettype($request->channels) != 'array') {
                $request['channels'] = json_decode($request->channels);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in channels'
                    ], 400);
                }
            }
            if (gettype($request->contactList) != 'array') {
                $request['contactList'] = json_decode($request->contactList);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in contactList'
                    ], 400);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                "status" => false,
                'message' => 'Invalid JSON in request.'
            ], 400);
        }

        $request->validate(
            [
                "campaignName" => "required|min:3|max:50",
                "channels" => "array|required",
                "contactList" => "array|required|max:5",
                "isFooterTimeEnable" => "required",
                "viewOnce" => "required",
                "startDate" => "required|date",
                "startTime" => "required",
                "enableEndTime" => "required",
                "stopDate" => "required_if:enableEndTime,true",
                "stopTime" => "required_if:enableEndTime,true",
                "sleepAfterMsgs" => "required|numeric|min:1",
                "sleepForSeconds" => "required|numeric|min:1",
            ],
            [],
            [
                "campaignName" => 'Campaign Name',
                "message" => 'Message',
                "isFooterTimeEnable" => 'Footer Time Enable',
                "viewOnce" => 'View Once',
                "sleepFor" => 'Sleep for',
                "sleepAfter" => 'Sleep After',
                "channels" => 'Channel',
                "startTime" => 'Start Time',
                "contactList" => 'Contact List',
                "mediaFiles" => 'Media',
            ],
        );

        DB::beginTransaction();
        try {
            $contactLists = ContactList::whereIn('id', $request->contactList)->withCount('contacts')->get();
            $Campaign = WaCampaign::find($id);
            $Campaign->name = $request->campaignName;
            $Campaign->msgFooterDateTime = $request->isFooterTimeEnable;
            $Campaign->viewOnce = $request->viewOnce;
            $Campaign->sleepAfterMsgs = $request->sleepAfterMsgs;
            $Campaign->sleepForSeconds = $request->sleepForSeconds;
            $Campaign->user_id = Auth::id();
            if ($request->has(["startDate", "startTime"])) {
                $formateStopDateTime = (new DateTime("{$request->startDate} {$request->startTime}"))->format('Y-m-d H:i:s');
                $Campaign->startTime = $formateStopDateTime;
            }

            if ($request->enableEndTime == true) {
                if ($request->has(["stopDate", "stopTime"])) {
                    $formateStopDateTime = (new DateTime("{$request->stopDate} {$request->stopTime}"))->format('Y-m-d H:i:s');
                    $Campaign->endTime = $formateStopDateTime;
                }
            } else {
                $Campaign->endTime = null;
            }

            $Campaign->totalContacts = $contactLists->sum('contacts_count'); // need contact list contacts

            $Campaign->save();
            $bulkContactListForCampaign = [];
            //delete old contact list
            WaBulkCampaignContactList::where('wa_campaign_id', $id)->delete();
            foreach ($request->contactList as $k => $cl) {
                $bulkContactListForCampaign[] = [
                    'wa_campaign_id' => $Campaign->id,
                    'contact_list_id' => $cl,
                    'user_id' => Auth::id(),
                ];
            }
            $bulkChannelsForCampaign = [];
            // delete old channels
            WaBulkCampaignChannel::where('wa_campaign_id', $id)->delete();
            foreach ($request->channels as $ch) {
                $bulkChannelsForCampaign[] = [
                    'wa_campaign_id' => $Campaign->id,
                    'wa_gateway_id' => $ch,
                    'user_id' => Auth::id(),
                ];
            }
            $Campaign->channels()->createManyQuietly(
                $bulkChannelsForCampaign
            );
            $Campaign->contactLists()->createManyQuietly(
                $bulkContactListForCampaign
            );

            DB::commit();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign Updated.", 'status' => true])
                :
                redirect()->route('whatsapp.campaign.create.message', ['campaign' => $Campaign->id])->with(["message" => "Campaign Updated.", "type" => "success"]);
        } catch (PDOException $e) {
            DB::rollback();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Fail to update Campaign.", 'status' => false])
                : redirect()->back()->with(["message" => "Fail to update Campaign.", "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }

        $isAdmin = request()->user()->isAdmin();
        $userIds = $isAdmin ? [] : request()->user()->getAllChildUserIds();

        try {
            $ids = explode(",", $id);
            $query = WaCampaign::whereIn('id', $ids);

            if (!$isAdmin) {
                $query->whereIn('user_id', $userIds);
            }

            $campaigns = $query->get();

            if ($campaigns->isEmpty()) {
                $message = 'Campaign not found.';
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => $message, 'status' => false])
                    : redirect()->back()->with(["message" => $message, "type" => "failure"]);
                // return response()->json(['message' => 'Campaign not found.', 'status' => false], 404);
            }

            $query->delete();

            return response()->json(['message' => 'Campaign Deleted.', 'status' => true]);
        } catch (PDOException $e) {
            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => false])
                : redirect()->back()->with(["message" => $message, "type" => "failure"]);
        }
    }

    public function campaignUpdateAddChannels(Request $r)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        DB::beginTransaction();
        try {

            $bulkChannelsForCampaign = [];
            foreach ($r->channels as $ch) {
                $array = [
                    'wa_campaign_id' => $r->id,
                    'wa_gateway_id' => $ch,
                    'user_id' => Auth::id(),
                ];
                if (WaBulkCampaignChannel::where($array)->exists() == false) {
                    $bulkChannelsForCampaign[] = $array;
                }
            }
            WaBulkCampaignChannel::insert($bulkChannelsForCampaign);
            DB::commit();
            session()->flash('message', 'Campaign Updated.');
            session()->flash("type", "success");
        } catch (PDOException $e) {
            DB::rollback();
            session()->flash('message', errorMessage());
            session()->flash("type", "failure");
            return false;
        }
    }

    public function campaignUpdateAddChannel(Request $r)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }
         try {
            $array = [
                'wa_campaign_id' => $r->campaign,
                'wa_gateway_id' => $r->gateway,
                'user_id' => Auth::id(),
            ];
            if (
                WaBulkCampaignChannel::where($array)->whereIn('user_id', function ($query) {
                    $query->select('id')
                        ->from((new User())->getTable())
                        ->whereRaw('id = ?', [Auth::id()])
                        ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
                })->exists()
            ) {
                $flashData["type"] = 'failure';
                $flashData["message"] = 'Channel already exist.';
            } else {
                WaBulkCampaignChannel::insert($array);
                $flashData["type"] = 'success';
                $flashData["message"] = 'Campaign Updated.';
            }

            session()->flash('message', $flashData["message"]);
            session()->flash("type", $flashData["type"]);
            return;
        } catch (PDOException $e) {
            session()->flash('message', errorMessage());
            session()->flash("type", "failure");
            return false;
        }
    }

    public function campaignUpdateAddContactList(Request $r)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        DB::beginTransaction();
        try {

            $bulkChannelsForCampaign = [];
            foreach ($r->channels as $ch) {
                $array = [
                    'wa_campaign_id' => $r->id,
                    'contact_list_id' => $ch,
                    // 'user_id' => Auth::id(),
                ];
                if (
                    WaBulkCampaignContactList::where($array)->whereIn('user_id', function ($query) {
                        $query->select('id')
                            ->from((new User())->getTable())
                            ->whereRaw('id = ?', [Auth::id()])
                            ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
                    })->exists() == false
                ) {
                    $bulkChannelsForCampaign[] = $array;
                }
            }
            WaBulkCampaignContactList::insert($bulkChannelsForCampaign);
            DB::commit();
            session()->flash('message', 'Campaign Updated.');
            session()->flash("type", "success");
        } catch (PDOException $e) {
            DB::rollback();
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
            return false;
        }
    }

    public function createMessage($campaign)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        try {
            $data['id'] = $campaign;
            $data['campaign'] = WaCampaign::with('pivotChannels', "pivotContactLists", "messages")
                ->where('id', $campaign)->whereIn('user_id', function ($query) {
                    $query->select('id')
                        ->from((new User())->getTable())
                        ->whereRaw('id = ?', [Auth::id()])
                        ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
                })->first();
            if ($data['campaign'] == null) {
                return redirect(route('whatsapp.campaign.index'));
            }
            return Inertia::render('WhatsApp/Campaign/Steps/AddMessage', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function storeMessage(Request $request)
    {
       if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        if (($request->varObject && count($request->varObject)) != $request->variable_count) {
            $errorMsgs['variable_error'] = "variable are required.";
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'unable to create Message', 'errors' => $errorMsgs, 'status' => false])
                : redirect()->back()->withErrors($errorMsgs);
        }
        if ($request->exists('template_id') && $request->template_id != null) {
            try {
                $camp_msg = new WaBulkCampaignMsg();

                $camp_msg->wa_campaign_id = $request->campaign_id;
                $camp_msg->wa_template_id = $request->template_id;
                if ($request->hasFile('attachment')) {
                    foreach ($request->file('attachment') as $value) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = storeFile('uploads/' . $this->tableName, $value, $fileName);
                        $camp_msg->file = $path;
                    }
                }
                   $camp_msg->body = $request->msg;
                $camp_msg->save();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Campaign Message Created.", 'status' => true])
                    : redirect()->back()->with(['message' => "Campaign Message Created.", 'type' => 'success']);
            } catch (Exception $e) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                    : redirect()->back()->with(['message' => errorMessage($e->getCode()), 'type' => 'failure']);
            }
        } else {
            $request->validate(
                [
                    'msg' => 'required_if:files,null',
                    'files' => 'required_without:msg',
                    'campaign_id' => 'required'
                ],
                [],
                ['msg' => 'Message', 'files' => 'file']
            );


            try {
                $camp_msg = new WaBulkCampaignMsg();
                $camp_msg->body = $request->msg;
                $camp_msg->wa_campaign_id = $request->campaign_id;

                if ($request->attachment) {
                    foreach ($request->file('attachment') as $value) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = storeFile( 'uploads/' . $this->tableName, $value, $fileName);
                        $camp_msg->file = $path;
                    }
                }
                $camp_msg->save();
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Campaign Message Created.", 'status' => true])
                    : redirect()->back();
            } catch (Exception $e) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                    : redirect()->back()->with(['message' => errorMessage($e->getCode()), 'type' => 'failure']);
            }
        }
    }

    public function getMessages($campaign)
    {
        if (!request()->user()->can($this->tableName . '.view')) {
            abort(403, "unauthorized");
        }
        $wantJson = request()->header('Accept') == 'application/json';
        try {
            $campaign = WaCampaign::where(['id' => $campaign])->whereIn('user_id', function ($query) {
                $query->select('id')
                    ->from((new User())->getTable())
                    ->whereRaw('id = ?', [Auth::id()])
                    ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
            })->with('messages')->first();
            if ($campaign) {
                $data['messages'] = $campaign->messages;
                $data['status'] = true;
                return response()->json($data);
            } else {
                return $wantJson ?
                    response()->json(['message' => "Messages not found .", 'status' => false])
                    : redirect()->back()->with(['message' => "Message not found.", "type" => "info"]);
            }
        } catch (PDOException $e) {
            session()->flash('message', errorMessage($e->getCode()));
            session()->flash("type", "failure");
            $data['status'] = false;
            $data['message'] = errorMessage(1);
            return response()->json($data);
        }
    }
    function deleteMessage($message)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }
        $wantJson = request()->header('Accept') == 'application/json';
        try {
            $msg = WaBulkCampaignMsg::find($message);
            if (!$msg) {
                return $wantJson ?
                    response()->json(['message' => "Message not found.", 'status' => false])
                    : redirect()->back()->with(['message' => "Message not found.", "type" => "info"]);
            }

            // if ($msg->campaign->user_id !== Auth::id()) {
            //     return $wantJson ?
            //         response()->json(['message' => "Message not found.", 'status' => false])
            //         : redirect()->back()->with(['message' => "Unauthorized action.", "type" => "success"]);
            // }

            if ($msg->file && $msg->isTemplateFile == 0 && Storage::disk('public')->exists($msg->file)) {
                Storage::disk('public')->delete($msg->file);
            }
            $msg->delete();
            session()->flash('message', "Message Deleted.");
            session()->flash("type", "success");
            return $wantJson ?
                response()->json(['message' => "Message Deleted.", 'status' => true])
                : redirect()->back();
        } catch (Exception $e) {
            if ($wantJson) {
                response()->json(['message' => errorMessage(), 'status' => false]);
            }
            if ($e->getCode() == 23000) {
                return redirect()->back()->with(['message' => errorMessage(5), "type" => "failure"]);
            }

            return redirect()->back()->with(['message' => errorMessage(5), "type" => "failure"]);
        }
    }
}
