<?php

namespace App\Http\Controllers\Voice;

use App\Http\Controllers\Controller;
use App\Models\Voice\TemplateCategory;
use App\Models\Voice\TemplateView;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TemplateCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $userIds = request()->user()->getAllChildUserIds();
            $data["categories"] = TemplateCategory::query();
            $data["categories"] = $data["categories"]->whereIn('user_id', $userIds);
            $data["categories"] = $data["categories"]->withCount('templates')->get();

            return response()->json(['collection' => $data, 'status' => true]);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $request->validate([
            "name" => "required|max:45|unique:" . (new TemplateCategory())->getTable() . ",name",
        ]);
        try {
            $category = new TemplateCategory();
            $category->name = $request->name;
            $category->user_id = Auth::id();
            $category->save();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Category Added", "type" => "success"])
                :
                redirect()->back()->with(["message" => "Category Added", "type" => "success"]);
        } catch (Exception $e) {

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "type" => "failure"])
                :
                redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {


        try {
            $totalCount = $_GET['perPage'] ?? 8;
            $userIds = request()->user()->getAllChildUserIds();
            $data['category'] = TemplateCategory::where(['id' => $id])->whereIn('user_id', $userIds)->first();;
            $templates = TemplateView::query();
            $templates = $templates->whereIn("user_id", $userIds);
            if ($id > 0) {
                $templates = $templates->where("mail_template_category_id", $id);
            }

            $templates = $templates->orderBy('datetime', 'desc');
            $data['templates'] = $templates->paginate($totalCount)->withQueryString();

            $data['getData'] = $_GET;

            // return response()->json(['collection' => $data, 'status' => true]);
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['collection' => $data])
                : Inertia::render('Voice/Templates/Index', ['collection' => $data]);
        } catch (Exception $e) {

            return response()->json(['status' => false, 'message' => errorMessage($e->getCode())]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            "name" => "required|max:45|unique:" . (new TemplateCategory())->getTable() . ",name," . $id . ",id",
        ]);
        try {
            $userIds = request()->user()->getAllChildUserIds();
            $category = TemplateCategory::where(['id' => $id])->whereIn('user_id', $userIds)->first();
            if (!$category) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(["message" => "Category not found", "type" => "failure"])
                    :
                    redirect()->back()->with(["message" => "Category not found", "type" => "failure"]);
            }
            $category->name = $request->name;
            $category->user_id = Auth::id();
            $category->save();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => "Category Added", "type" => "success"])
                :
                redirect()->back()->with(["message" => "Category Added", "type" => "success"]);
        } catch (Exception $e) {

            return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => errorMessage($e->getCode()), "type" => "failure"])
                :
                redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {

        $ids = explode(",", $id);


        try {
            $userIds = request()->user()->getAllChildUserIds();
            $category = TemplateCategory::whereIn('id', $ids)->whereIn('user_id', $userIds)->get();

            if (empty($category)) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => "Category not found.", 'status' => false])
                    : redirect()->back()->with(["message" => "Category not found.", "type" => "failure"]);
            }
            (TemplateCategory::whereIn('id', $ids)->whereIn('user_id', $userIds)->delete());
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Category deleted.", 'status' => true])
                : redirect()->back()->with(["message" => "Category deleted.", "type" => "success"]);
        } catch (Exception $e) {

            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => $message, 'status' => false])
                : redirect()->back()->with(["message" => $message, "type" => "failure"]);
        }
    }
}
