<?php

namespace App\Http\Controllers\WhatsappBusiness\Bot;

use App\Http\Controllers\Controller;
use App\Models\WhatsappBusiness\bot\WaBaBot;
use App\Models\WhatsappBusiness\bot\WaBaBotKeyword;
use App\Models\WhatsappBusiness\bot\WaBaBotKeywordMsg;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class KeywordMessageController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBaBotKeywordMsg())->getTable();
    }

    /**
     * Display a listing of the resource.
     */

    public function isMyKeyword($keyword)
    {
        $Keyword = WaBaBotKeyword::where(['user_id' => Auth::id(), 'id' => $keyword])->first();
        if ($Keyword) {
            return $Keyword;
        } else {
            return false;
        }
    }

    public function index($keyword)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        $keywordData = $this->isMyKeyword($keyword);
        if (!$keywordData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        try {

            $search = request()->query('search', null);
            $perPage = request()->query('perPage', 10);
            $column = request()->query('sortColumn', 'id');
            $direction = request()->query('sortBy', 'desc');
            $messages = WaBaBotKeywordMsg::orderBy($column, $direction)
                ->where('waba_bot_keyword_id', $keyword)
                ->when($search, function ($query, $search) {
                    return $query->whereAny(['body', 'file', 'id'], 'LIKE', "%{$search}%");
                })
                ->paginate($perPage)->withQueryString();
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'collection' => ['messages' => $messages, 'keyword' => $keywordData, "can_add" => request()->user()->can($this->tableName . ".add"), "can_edit" => request()->user()->can($this->tableName . ".edit"), "can_delete" => request()->user()->can($this->tableName . ".delete")], 'message' => 'Messages fetched successfully']) :
                Inertia::render('WhatsappBusiness/Bot/Keywords/Message/Index', [
                    'collection' =>
                    ['keyword' => $keywordData, 'messages' => $messages, 'getData' => $_GET, "can_add" => request()->user()->can($this->tableName . ".add"), "can_edit" => request()->user()->can($this->tableName . ".edit"), "can_delete" => request()->user()->can($this->tableName . ".delete")]
                ]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($keyword)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }
        try {
            $keywordData = $this->isMyKeyword($keyword);
            if (!$keywordData) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Unauthorized', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
            }
            // show form to create a new message
            return Inertia::render('WhatsappBusiness/Bot/Keywords/Messages/Add');
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $keyword)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        $keywordData = $this->isMyKeyword($keyword);
        if (!$keywordData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        $request->validate(
            [
                'message' => 'bail|required_without:attachment',
                'attachment.*' => 'required_without:message|file|max:20000',
            ],
            [],
            ['attachment' => 'File', 'message' => 'Message', 'attachment.*' => 'File']
        );
        
        try {
            $keywordMessage = new WaBaBotKeywordMsg();
            $keywordMessage->waba_bot_keyword_id = $keyword;
            $keywordMessage->body = $request->message;
            if ($request->hasFile('attachment')) {

                foreach ($request->attachment as $file) {
                    $fileName = time() . '-' . $file->getClientOriginalName();
                    $path = storeFile('uploads/' . $this->tableName, $file, $fileName);
                    $keywordMessage->file = $path;
                }
            }
            $keywordMessage->save();
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Message added successfully']) :
                redirect()->back()
                ->with(['message' => 'Message added successfully', 'type' => 'success']);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => false, 'message' => errorMessage($e->getCode())]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($keyword, string $id)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }
        try {
            $keywordData = $this->isMyKeyword($keyword);
            if (!$keywordData) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Unauthorized', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
            }
            // return the message
            $data = WaBaBotKeywordMsg::find($id);
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Message Found', 'data' => $data]) :
                Inertia::render('WhatsappBusiness/Bot/Keywords/Messages/Show', ['message' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($keyword, string $id)
    {
        $keywordData = $this->isMyKeyword($keyword);
        if (!$keywordData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $keyword, string $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
        try {
            $keywordData = $this->isMyKeyword($keyword);
            if (!$keywordData) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Unauthorized', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
            }
            $request->validate(
                [
                    'message' => 'bail|required_without:attachment',
                    'attachment.*' => 'required_without:message|file|max:20000',
                ],
                [
                    "attachment.required_without" => "Please ensure that you provide either a message or a file upload, or both.",
                    "message.required_without" => "Please ensure that you provide either a message or a file upload, or both.",
                ]
            );

            $message = WaBaBotKeywordMsg::where(['id' => $id, 'waba_bot_keyword_id' => $keyword])->first();
            $message->body = $request->message;

            if ($request->hasFile('attachment')) {
                deleteFile($message->file);
                foreach ($request->attachment as $file) {
                    $fileName = time() . '-' . $file->getClientOriginalName();
                    $path = storeFile('uploads/' . $this->tableName, $file, $fileName);
                    $message->file = $path;
                }
            }
            $message->update();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Updated Successfully']) :
                redirect()->back()->with(['message' => 'Updated Successfully', 'type' => 'success']);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => false, 'message' => errorMessage($e->getCode())]) :
                redirect()->back()->with(['message' => errorMessage($e->getCode()), 'type' => 'failure']);
        }


        //

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($keyword, string $id)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }

        $keywordData = $this->isMyKeyword($keyword);
        if (!$keywordData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        $ids = explode(',', $id);

        try {
            $messages = WaBaBotKeywordMsg::whereIn('id', $ids)->where(['waba_bot_keyword_id' => $keyword])->get();

            if ($messages->count() == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Message not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Message not found', "type" => "failure"]);
            }
            WaBaBotKeywordMsg::whereIn('id', $ids)->where(['waba_bot_keyword_id' => $keyword])->delete();
            foreach ($messages as $message) {
                deleteFile($message->file);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Message deleted.', 'status' => true]) :
                redirect()->back()->with(["message" => 'Message deleted.', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => false, 'message' => errorMessage($e->getCode())]) :
                redirect()->back()->with(['message' => errorMessage($e->getCode()), 'type' => 'failure']);
        }

        //
    }
}
