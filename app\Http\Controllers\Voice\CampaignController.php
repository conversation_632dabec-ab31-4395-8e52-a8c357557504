<?php

namespace App\Http\Controllers\Voice;

use App\Models\Voice\Campaign;
use App\Models\Voice\CampaignAttachment;
use App\Models\Voice\CampaignContactList;
use App\Models\Voice\CampaignGateway;
use App\Models\Voice\Gateway;
use DateTime;
use Exception;
use PDOException;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\User;

use App\Models\ContactList;

use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class CampaignController extends Controller
{

    private $tableName;
    public function __construct()
    {
        $this->tableName = (new Campaign())->getTable();
    }

    /**
     * Display a listing of the resource.
     */

    public function index()
    {
        

        
        // if (!request()->user()->can($this->tableName . ".view")) {
        //     abort(403, "unauthorized");
        // }
        $isAdmin = request()->user()->isAdmin();
        try {
            $totalCount = request()->query('perPage', 10);
            $column = request()->query('column', 'id');
            $sortBy = request()->query('sort', 'desc');

            $query = Campaign::orderBy($column, $sortBy);
            $getUser = request()->query("userID");
            $userIds = request()->user()->getAllChildUserIds()->toArray();
            if ($getUser) {
                $user = User::find($getUser);
                $getUserIds = $user->getAllChildUserIds()->toArray();
                if ($isAdmin || in_array($user->id, $userIds)) {
                    $query->whereIn("user_id", $getUserIds);
                } else {
                    return request()->header('Accept') == 'application/json'
                        ? response()->json(['message' => 'Invalid request.', 'status' => false], 400)
                        : redirect()->back()->with(['message' => 'Invalid request.', 'type' => "failure"]);
                }
            } elseif (!$isAdmin) {
                $query->whereIn("user_id", $userIds);
            }

            $backData["campaigns"] = $query->with('gateways', 'contactLists', 'user')
                ->paginate($totalCount)
                ->withQueryString()
                ->toArray();
            $backData['getData'] = request()->query();
            $backData["can_add"] = request()->user()->can($this->tableName . ".add");
            $backData["can_edit"] = request()->user()->can($this->tableName . ".edit");
            $backData["can_delete"] = request()->user()->can($this->tableName . ".delete");

            return request()->header('Accept') == 'application/json'
                ? response()->json(['status' => true, 'collection' => $backData])
                : Inertia::render('Voice/Campaign/Index', ['collection' => $backData]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */

    public function create()
    {
        // if (!request()->user()->can($this->tableName . '.add')) {
        //     abort(403, "unauthorized");
        // }
        return Inertia::render('Voice/Campaign/Steps/Add');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        
        // if (!request()->user()->can($this->tableName . '.add')) {
        //     abort(403, "unauthorized");
        // }
        
        try {
            $request['gateways'] = is_array($request->gateways) ? $request->gateways : json_decode($request->gateways, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    "status" => false,
                    'message' => 'Invalid JSON in gateways'
                ], 400);
            }

            $request['contactList'] = is_array($request->contactList) ? $request->contactList : json_decode($request->contactList, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    "status" => false,
                    'message' => 'Invalid JSON in contactList'
                ], 400);
            }
        } catch (Exception $e) {
            
            return response()->json([
                "status" => false,
                'message' => 'Invalid JSON in request.'
            ], 400);
        }
        
        $request->validate(
            [
                "campaignName" => "required|min:3|max:50",
                "gateways" => "array|required",
                "contactList" => "array|required|max:5",
                "startDate" => "required|date",
                "startTime" => "required",
                "enableEndTime" => "required",
                "stopDate" => "required_if:enableEndTime,true",
                "stopTime" => "required_if:enableEndTime,true",
                "sleepAfterMsgs" => "required|numeric|min:1",
                "sleepForSeconds" => "required|numeric|min:1",
            ],
            [],
            [
                "campaignName" => 'Campaign Name',
                "message" => 'Message',
                "viewOnce" => 'View Once',
                "sleepFor" => 'Sleep for',
                "sleepAfter" => 'Sleep After',
                "gateways" => 'Gateways',
                "startTime" => 'Start Time',
                "contactList" => 'Contact List',
                "mediaFiles" => 'Media',
            ],
        );
        DB::beginTransaction();
        try {
            

            $contactLists = ContactList::whereIn('id', $request->contactList)->withCount('contacts')->get();
            $Campaign = new Campaign();
            $Campaign->name = $request->campaignName;
            $Campaign->sleepAfterMsgs = $request->sleepAfterMsgs;
            $Campaign->sleepForSeconds = $request->sleepForSeconds;
            $Campaign->user_id = Auth::id();
            if ($request->has(["startDate", "startTime"])) {
                $formateStopDateTime = (new DateTime("{$request->startDate} {$request->startTime}"))->format('Y-m-d H:i:s');
                $Campaign->startDatetime = $formateStopDateTime;
            }

            if ($request->enableEndTime == true) {
                if ($request->has(["stopDate", "stopTime"])) {
                    $formateStopDateTime = (new DateTime("{$request->stopDate} {$request->stopTime}"))->format('Y-m-d H:i:s');
                    $Campaign->endDatetime = $formateStopDateTime;
                }
            }

            $Campaign->totalContacts = $contactLists->sum('contacts_count'); // need contact list contacts

            $Campaign->save();
            $bulkContactListForCampaign = [];
            foreach ($request->contactList as $k => $cl) {
                $bulkContactListForCampaign[] = [
                    'mail_campaign_id' => $Campaign->id,
                    'contact_list_id' => $cl,
                ];
            }
            $bulkGatewaysForCampaign = [];
            foreach ($request->gateways as $ch) {
                $bulkGatewaysForCampaign[] = [
                    'mail_campaign_id' => $Campaign->id,
                    'mail_gateway_id' => $ch,
                ];
            }
            CampaignGateway::insert($bulkGatewaysForCampaign);
            CampaignContactList::insert($bulkContactListForCampaign);
            DB::commit();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign Created.", 'status' => true])
                :
                redirect()->route('mail.campaign.create.message', ['campaign' => $Campaign->id])->with(["message" => "Campaign Created.", "type" => "success"]);
        } catch (PDOException $e) {
            
            DB::rollback();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // if (!request()->user()->can($this->tableName . '.view')) {
        //     abort(403, "unauthorized");
        // }

        $userId = $_GET["userID"] ?? Auth::id();
        $userIds = request()->user()->getAllChildUserIds();
        try {


            $backData['campaign'] = Campaign::where('id', $id)
                ->whereIn('user_id', $userIds)
                ->with(['gateways', 'contactLists', 'user:id,username', 'attachments'])
                ->first();
            // ->withCount('failed', 'sent', 'nonWhatsapp')
            if (empty($backData['campaign'])) {
                return response()->json(['message' => 'Invalid Request.', 'status' => false]);
            }
            if (request()->header('Accept') == 'application/json') {
                return response()->json(['collection' => $backData, 'status' => true]);
            }
            $backData['gateways'] = Gateway::whereIn('user_id', $userIds)->get();
            $backData['id'] = $id;
            $allChannelIds = array_flip(array_column($backData["campaign"]->gateways->toArray(), 'id'));
            $backData["remainingGateways"] = array_values(array_filter($backData["gateways"]->toArray(), function ($record) use ($allChannelIds) {
                return !isset($allChannelIds[$record['id']]);
            }));
            return response()->json(['collection' => $backData, 'status' => true]);
        } catch (Exception $e) {
            
            return response()->json(['message' => errorMessage($e->getCode()), 'status' => false]);
            //    (request()->header('Accept') == 'application/json') ?
            // : redirect()->back()->with(["message" => errorMessage(3), "type" => "failure"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $campaign)
    {
        // if (!request()->user()->can($this->tableName . '.edit')) {
        //     abort(403, "unauthorized");
        // }

        try {
            $data['id'] = $campaign;
            $query = Campaign::where('id', $campaign);
            if (!request()->user()->isAdmin()) {
                $userIds = request()->user()->getAllChildUserIds();
                $query->whereIn('user_id', $userIds);
            }
            $query->with('gateways', "contactLists");
            $data['campaign'] =  $query->first();

            if ($data['campaign'] == null) {
                return redirect(route('mail.campaign.index'))->with(["message" => "Campaign not found.", "type" => "failure"]);
            }
            
            $data['gateways'] = $data['campaign']->gateways()->pluck('mail_gateway_id');
            $data['contactLists'] = $data['campaign']->contactLists()->pluck('contact_list_id');
            
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['collection' => $data, 'status' => true])
                : Inertia::render('Voice/Campaign/Steps/Edit', ['collection' => $data]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Update the specified resource in storage.
     */

    public function update(Request $request, string $id)
    {
        // if (!request()->user()->can($this->tableName . '.edit')) {
        //     abort(403, "unauthorized");
        // }
        
        try {
            if (gettype($request->gateways) != 'array') {
                $request['gateways'] = json_decode($request->gateways);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in gateways'
                    ], 400);
                }
            }
            if (gettype($request->contactList) != 'array') {
                $request['contactList'] = json_decode($request->contactList);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in contactList'
                    ], 400);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                "status" => false,
                'message' => 'Invalid JSON in request.'
            ], 400);
        }

        $request->validate(
            [
                "campaignName" => "required|min:3|max:50",
                "gateways" => "array|required",
                "contactList" => "array|required|max:5",
                "isFooterTimeEnable" => "required",
                "viewOnce" => "required",
                "startDate" => "required|date",
                "startTime" => "required",
                "enableEndTime" => "required",
                "stopDate" => "required_if:enableEndTime,true",
                "stopTime" => "required_if:enableEndTime,true",
                "sleepAfterMsgs" => "required|numeric|min:1",
                "sleepForSeconds" => "required|numeric|min:1",
            ],
            [],
            [
                "campaignName" => 'Campaign Name',
                "message" => 'Message',
                "isFooterTimeEnable" => 'Footer Time Enable',
                "viewOnce" => 'View Once',
                "sleepFor" => 'Sleep for',
                "sleepAfter" => 'Sleep After',
                "gateways" => 'Gateways',
                "startTime" => 'Start Time',
                "contactList" => 'Contact List',
                "mediaFiles" => 'Media',
            ],
        );

        DB::beginTransaction();
        try {
            $contactLists = ContactList::whereIn('id', $request->contactList)->withCount('contacts')->get();
            $Campaign = Campaign::find($id);
            $Campaign->name = $request->campaignName;

            $Campaign->sleepAfterMsgs = $request->sleepAfterMsgs;
            $Campaign->sleepForSeconds = $request->sleepForSeconds;
            $Campaign->user_id = Auth::id();
            if ($request->has(["startDate", "startTime"])) {
                $formateStopDateTime = (new DateTime("{$request->startDate} {$request->startTime}"))->format('Y-m-d H:i:s');
                $Campaign->startDatetime = $formateStopDateTime;
            }

            if ($request->enableEndTime == true) {
                if ($request->has(["stopDate", "stopTime"])) {
                    $formateStopDateTime = (new DateTime("{$request->stopDate} {$request->stopTime}"))->format('Y-m-d H:i:s');
                    $Campaign->endDatetime = $formateStopDateTime;
                }
            } else {
                $Campaign->endDatetime = null;
            }

            $Campaign->totalContacts = $contactLists->sum('contacts_count'); // need contact list contacts

            $Campaign->save();
            $bulkContactListForCampaign = [];
            //delete old contact list
            // $Campaign->gateways()->delete();
            // $Campaign->contactLists()->delete();
            CampaignContactList::where('mail_campaign_id', $id)->delete();
            foreach ($request->contactList as $k => $cl) {
                $bulkContactListForCampaign[] = [
                    'mail_campaign_id' => $Campaign->id,
                    'contact_list_id' => $cl,
                ];
            }
            $bulkGatewayForCampaign = [];
            // delete old gateway
            CampaignGateway::where('mail_campaign_id', $id)->delete();
            foreach ($request->gateways as $ch) {
                $bulkGatewayForCampaign[] = [
                    'mail_campaign_id' => $Campaign->id,
                    'mail_gateway_id' => $ch,

                ];
            }
            $Campaign->gateways()->sync(
                $bulkGatewayForCampaign
            );
            $Campaign->contactLists()->sync(
                $bulkContactListForCampaign
            );

            DB::commit();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign Updated.", 'status' => true])
                :
                redirect()->route('mail.campaign.create.message', ['campaign' => $Campaign->id])->with(["message" => "Campaign Updated.", "type" => "success"]);
        } catch (PDOException $e) {
            
            DB::rollback();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Fail to update Campaign.", 'status' => false])
                : redirect()->back()->with(["message" => "Fail to update Campaign.", "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // if (!request()->user()->can($this->tableName . '.delete')) {
        //     abort(403, "unauthorized");
        // }

        $isAdmin = request()->user()->isAdmin();
        $userIds = $isAdmin ? [] : request()->user()->getAllChildUserIds();
        $message = "campaign deleted";
        try {
            $ids = explode(",", $id);
            $query = Campaign::whereIn('id', $ids);

            if (!$isAdmin) {
                $query->whereIn('user_id', $userIds);
            }

            $campaigns = $query->get();

            if ($campaigns->isEmpty()) {
                $message = 'Campaign not found.';
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => $message, 'status' => false])
                    : redirect()->back()->with(["message" => $message, "type" => "failure"]);
            }
            foreach ($campaigns as $campaign) {
                $campaign->gateways()->delete();
                $campaign->contactLists()->delete();
            }

            $query->delete();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => true])
                : redirect()->back()->with(["message" => $message, "type" => "success"]);
        } catch (PDOException $e) {
            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => false])
                : redirect()->back()->with(["message" => $message, "type" => "failure"]);
        }
    }

    public function campaignUpdateAddChannels(Request $r)
    {
        // if (!request()->user()->can($this->tableName . '.update')) {
        //     abort(403, "unauthorized");
        // }
        DB::beginTransaction();
        try {

            $bulkChannelsForCampaign = [];
            foreach ($r->channels as $ch) {
                $array = [
                    'mail_campaign_id' => $r->id,
                    'mail_gateway_id' => $ch,
                ];
                if (CampaignGateway::where($array)->exists() == false) {
                    $bulkChannelsForCampaign[] = $array;
                }
            }
            CampaignGateway::insert($bulkChannelsForCampaign);
            DB::commit();
            session()->flash('message', 'Campaign Updated.');
            session()->flash("type", "success");
        } catch (PDOException $e) {
            
            DB::rollback();
            session()->flash('message', errorMessage());
            session()->flash("type", "failure");
            return false;
        }
    }

    public function campaignUpdateAddChannel(Request $r)
    {
        // if (!request()->user()->can($this->tableName . '.edit')) {
        //     abort(403, "unauthorized");
        // }
        
        try {
            $array = [
                'mail_campaign_id' => $r->data['campaign'],
                'mail_gateway_id' => $r->data['gateway'],
                // 'user_id' => Auth::id(),
            ];
            // $userIds = request()->user()->getAllChildUserIds();
            if (CampaignGateway::where($array)->exists()) {
                $flashData["type"] = 'failure';
                $flashData["message"] = 'Channel already exist.';
            } else {
                CampaignGateway::insert($array);
                $flashData["type"] = 'success';
                $flashData["message"] = 'Campaign Updated.';
            }
            session()->flash('message', $flashData["message"]);
            session()->flash("type", $flashData["type"]);
            return;
        } catch (Exception $e) {
            
            session()->flash('message', errorMessage($e->getCode()));
            session()->flash("type", "failure");
            return false;
        }
    }

    public function campaignUpdateAddContactList(Request $r)
    {
        // if (!request()->user()->can($this->tableName . '.add')) {
        //     abort(403, "unauthorized");
        // }
        DB::beginTransaction();
        try {

            $bulkChannelsForCampaign = [];
            foreach ($r->channels as $ch) {
                $array = [
                    'mail_campaign_id' => $r->id,
                    'contact_list_id' => $ch,
                ];
                if (
                    CampaignContactList::where($array)->whereIn('user_id', function ($query) {
                        $query->select('id')
                            ->from((new User())->getTable())
                            ->whereRaw('id = ?', [Auth::id()])
                            ->orWhereRaw('parentUser_id = ?', [Auth::id()]);
                    })->exists() == false
                ) {
                    $bulkChannelsForCampaign[] = $array;
                }
            }
            CampaignContactList::insert($bulkChannelsForCampaign);
            DB::commit();
            session()->flash('message', 'Campaign Updated.');
            session()->flash("type", "success");
        } catch (PDOException $e) {
            DB::rollback();
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
            return false;
        }
    }

    public function createMessage($campaign)
    {
        
        try {
            $data['id'] = $campaign;
            $userIds = request()->user()->getAllChildUserIds();
            $data['campaign'] = Campaign::where('id', $campaign)->whereIn('user_id', $userIds)->with('attachments', 'variables')->first();
            if ($data['campaign'] == null) {
                return redirect(route('mail.campaign.index'));
            }
            return Inertia::render('Voice/Campaign/Steps/AddMessage', ['collection' => $data]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function storeMessage(Request $request)
    {
        
        // // if (!request()->user()->can($this->tableName . '.add')) {
        // //     abort(403, "unauthorized");
        // // }
        if (($request->varObject && count($request->varObject)) != $request->variable_count) {
        }
        if ($request->variable_count != 0 && !$this->validateVarObject($request)) {
            $errorMsgs['variable_error'] = "Variable(s) are required.";
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'unable to create Message', 'errors' => $errorMsgs, 'status' => false])
                : redirect()->back()->withErrors($errorMsgs);
        }
        
        $varObject = $request->input('varObject');
        $insertVariables = [];
        if ($varObject) {
            foreach ($varObject as $key => $variable) {
                $insertVariables[] = [
                    'mail_campaign_id' => $request->campaign_id,
                    'key' => $key,
                    'valueFixed' => $variable['fixed'],
                    'valueField' => $variable['column'],
                ];
            }
        }


        $request->validate(
            [
                'message' => 'required_without:files',
                'files' => 'required_without:message',
                'campaign_id' => 'required'
            ],
            [],
            ['message' => 'Message', 'files' => 'Attachments']
        );


        try {
            $campaign =  Campaign::find($request->campaign_id);
            $campaign->msg = $request->message;
            $campaign->mail_template_id = $request->template_id;

            if ($request->hasFile('files')) {
                $campaignFile = [];
                foreach ($request->file('files') as $value) {
                    $fileName = time() . '-' . $value->getClientOriginalName();
                    $path = storeFile(
                        'uploads/' . $this->tableName . '/MailCampaignAttachments',
                        $value,
                        $fileName,
                    );
                    $campaignFile[] = [
                        'path' => $path,
                        'mail_campaign_id' => $campaign->id
                    ];
                }
                $campaign->attachments()->createManyQuietly(
                    $campaignFile
                );
            }

            if ($varObject) {
                $campaign->variables()->createManyQuietly($insertVariables);
            }
            $campaign->update();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign Message and Attachments Stored.", 'status' => true])
                : redirect()->route('mail.campaign.index')->with(['message' => "Campaign Message and Attachments Stored.", 'type' => 'success']);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(['message' => errorMessage($e->getCode()), 'type' => 'failure']);
        }
        // }
    }
    public function validateVarObject($request)
    {
        if (!isset($request['varObject']) || !is_array($request['varObject'])) {
            return false; // varObject missing or not an array
        }

        foreach ($request['varObject'] as $variable => $config) {
            if (!is_array($config) || !isset($config['column']) && !isset($config['fixed'])) {
                return false; // Invalid config structure
            }

            if (empty($config['column']) && empty($config['fixed'])) {
                return false; // Both column and fixed are null or empty
            }
        }

        return true; // All varObject entries are valid
    }

    public function removeAttachment($id)
    {
        try {

            $CampaignAttachment = CampaignAttachment::find($id);
            if ($CampaignAttachment) {
                $CampaignAttachment->delete();
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Attachment removed.", 'status' => true])
                : redirect()->back()->with(['message' => 'Attachment removed.', 'type' => 'success']);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(['message' => errorMessage($e->getCode()), 'type' => 'failure']);
        }
    }
}
