import { button_custom } from '@/Pages/Helpers/DesignHelper';
import { useForm } from '@inertiajs/react';
import { Button, Label, TextInput } from 'flowbite-react';
import { memo, useState } from 'react';

const Edit = memo(({ onClose, gateway }) => {
    const formFields = [
        { id: 'name', label: 'Name:', placeholder: 'e.g., server 1', helperText: 'Name for future reference.' },
        { id: 'host', label: 'Host:', placeholder: 'e.g., smtp.gmail.com' },
        { id: 'port', label: 'Port:', type: 'number', placeholder: 'e.g., 587' },
        { id: 'username', label: 'Username:', placeholder: 'e.g., <EMAIL>' },
        { id: 'password', label: 'Password:', placeholder: 'Your Password' },
        { id: 'limit', label: 'Limit Per Minute:', type: 'number', min: 1, placeholder: '5', helperText: 'Set limit for send mail per minute.' }
    ];

    const { data, setData, post, errors } = useForm({
        name: gateway.name ?? '',
        host: gateway.host ?? '',
        port: gateway.port ?? '',
        username: gateway.username ?? '',
        password: gateway.password ?? '',
        limit: gateway.limit_per_minute ?? '',
    });

    const [formLoader, setFormLoader] = useState(false);

    const handleSubmit = (e) => {
        e.preventDefault();
        setFormLoader(true);
        post(route('mail.gateways.update', { id: gateway.id }), {
            onSuccess: () => {
                onClose();
                setFormLoader(false);
            },
            onError: () => setFormLoader(false)
        });
    };

    return (
        <div className="w-full">
            <form onSubmit={handleSubmit}>
                {formFields.map(({ id, label, type = 'text', placeholder, helperText, min }) => (
                    <div key={id} className="mb-4">
                        <Label htmlFor={id}>{label}</Label>
                        <TextInput
                            id={id}
                            name={id}
                            type={type}
                            min={min}
                            value={data[id]}
                            onChange={e => setData(id, e.target.value)}
                            placeholder={placeholder}
                            color={errors[id] && 'failure'}
                            helperText={errors[id] || helperText}
                            className="mt-1"
                        />
                    </div>
                ))}
                
                <div className="flex justify-end mb-4">
                    <Button 
                        theme={button_custom} 
                        size='sm' 
                        isProcessing={formLoader} 
                        disabled={formLoader} 
                        type="submit" 
                        color="blue"
                    >
                        Update
                    </Button>
                </div>
            </form>
        </div>
    );
});

export default Edit;
