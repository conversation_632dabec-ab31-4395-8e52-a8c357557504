<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\WaBot;
use App\Models\WaBotKeyword;
use App\Models\WaBotKeywordMsg;
use Exception;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use PDOException;

class BotKeywordController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBotKeyword())->getTable();
    }


    public function isMyBot($bot)
    {
        $bot = WaBot::where(['id' => $bot])->first();
        if ($bot->user_id == Auth::id() || $bot->user->parentUser_id == Auth::id()) {
            return $bot;
        } else {
            return false;
        }
    }

    public function index($bot)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        try {
            $search = request()->query('search', null);
            $perPage = request()->query('perPage', 10);
            $column = request()->query('sortColumn', 'id');
            $direction = request()->query('sortBy', 'desc');

            if (request()->header('Accept') == 'application/json') {
                $messages = WaBotKeyword::orderBy($column, $direction)
                    ->where('wa_bot_id', $bot)
                    ->when($search, function ($query, $search) {
                        return $query->whereAny(['keyword', 'id'], 'LIKE', "%" . $search . "%");
                    })
                    ->withCount('messages')
                    ->paginate($perPage)->withQueryString();
                return response()->json(['status' => true, 'collection' => ["data" => $messages, "can_add" => request()->user()->can($this->tableName . ".add"), "can_edit" => request()->user()->can($this->tableName . ".edit"), "can_delete" => request()->user()->can($this->tableName . ".delete")], 'message' => 'Messages fetched successfully']);
            } else {
                $messages = WaBotKeyword::orderBy($column, $direction)
                    ->where('wa_bot_id', $bot)
                    ->when($search, function ($query, $search) {
                        return $query->whereAny(['keyword', 'id'], 'LIKE', "%" . $search . "%");
                    })
                    ->withCount('messages')
                    ->paginate($perPage)->withQueryString();
                return Inertia::render('WhatsApp/Bot/Keywords/Index', ["collection" => ['keywords' => $messages, 'getData' => $_GET, 'bot' => $botData, "can_add" => request()->user()->can($this->tableName . ".add"), "can_edit" => request()->user()->can($this->tableName . ".edit"), "can_delete" => request()->user()->can($this->tableName . ".delete")]]);
            }
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function create($bot)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        return Inertia::render('WhatsApp/Bot/Steps/Add');
    }

    public function store(Request $request, $bot)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        $request->validate([
            'keyword' => 'required|string',
            'enableAddMessage' => 'required'
        ]);
        try {
            $keyword = new WaBotKeyword();
            $keyword->keyword = $request->keyword;
            $keyword->wa_bot_id = $bot;
            $keyword->user_id = Auth::id();

            if ($request->enableAddMessage == 1) {
                $validator = Validator::make(
                    $request->all(),
                    [
                        'msg' => 'bail|required_without:attachment',
                        'attachment.*' => 'required_without:msg|file|max:20000',
                    ],
                    [],
                    ['attachment' => 'File', 'msg' => 'Message', 'attachment.*' => 'File']
                );
                if ($validator->fails()) {
                    return (request()->header('Accept') == 'application/json')
                        ? response()->json(['message' => 'Unable to add Keyword.', 'errors' => $validator->errors(), 'status' => false])
                        : redirect()->back()->withErrors($validator->errors());
                }

                $keyword->save();
                $keywordMessage = new WaBotKeywordMsg();
                $keywordMessage->wa_bot_keyword_id = $keyword->id;
                $keywordMessage->body = $request->msg;

                if ($request->hasFile('attachment')) {
                    foreach ($request->attachment as $file) {
                        $fileName = time() . '-' . $file->getClientOriginalName();
                        $path = storeFile('uploads/' . $this->tableName, $file, $fileName);
                        $keywordMessage->file = $path;
                    }
                }
                $keywordMessage->save();

                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['status' => true, 'message' => 'Keyword added successfully']) :
                    redirect()->back()->with(["message" => 'Keyword added successfully', "type" => "success"]);
            }
            $keyword->save();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Keyword added successfully']) :
                redirect()->back()->with(["message" => 'Keyword added successfully', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    public function show($bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        try {
            $keyword = WaBotKeyword::with('messages', 'bot')->find($id);
            if ($keyword->wa_bot_id != $bot) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Invalid Bot ID', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Invalid Bot ID', "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'data' => $keyword, 'message' => 'Keyword fetched successfully']) :
                Inertia::render('WhatsApp/Bot/Keywords/Show', ['keyword' => $keyword]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function edit($bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }

        try {
            $keyword = WaBotKeyword::find($id);
            if ($keyword->wa_bot_id != $bot) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Invalid Bot ID', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Invalid Bot ID', "type" => "failure"]);
            }
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'data' => $keyword, 'message' => 'Keyword fetched successfully']) :
                Inertia::render('WhatsApp/Bot/Keywords/Edit', ['keyword' => $keyword]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function update(Request $request, $bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".edit")) {
            abort(403, "unauthorized");
        }
        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        
        $request->validate([
            'keyword' => 'required|string',
        ]);
        try {
            $keyword = WaBotKeyword::find($id);
            
            if ($keyword->wa_bot_id != $bot) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Invalid Bot ID', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Invalid Bot ID', "type" => "failure"]);
            }
            $keyword->keyword = $request->keyword;
            $keyword->update();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Keyword updated successfully']) :
                redirect()->back()->with(["message" => 'Keyword updated successfully', "type" => "success"]);
        } catch (Exception $e) {
            
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function destroy($bot, $id)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }

        $botData = $this->isMyBot($bot);
        if (!$botData) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Unauthorized', 'status' => false]) :
                redirect()->back()->with(["message" => 'Unauthorized', "type" => "failure"]);
        }
        try {
            $ids = explode(',', $id);
            // return $ids;
            $keyword = WaBotKeyword::whereIn('id', $ids)->where('wa_bot_id', $bot)->get();
            // return $keyword;
            if ($keyword->count() == 0) {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Keyword not found', 'status' => false]) :
                    redirect()->back()->with(["message" => 'Keyword not found', "type" => "failure"]);
            }
            WaBotKeyword::whereIn('id', $ids)->where('wa_bot_id', $bot)->delete();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['status' => true, 'message' => 'Keyword deleted successfully']) :
                redirect()->back()->with('success', 'Keyword deleted successfully');
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false]) :
                redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    // public function listKeyword($id)
    // {
    //     $totalCount = $_GET['perPage'] ?? 10;
    //     $column = $_GET['column'] ?? 'id';
    //     $sortBy = $_GET['sort'] ?? 'asc';
    //     $data["autoreply"] = WaBot::find($id);
    //     $data["keywords"] = WaBotKeyword::orderBy($column, $sortBy)->with("user")->where("wa_bot_id", $id)->paginate($totalCount)->withQueryString();
    //     $data['getData'] = $_GET;
    //     return Inertia::render('WhatsApp/Bot/Keywords/ListKeyword', ["keywordList" => $data]);
    // }
    public function keywordCreate($autoreplyId)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }
        return Inertia::render('WhatsApp/Bot/Keywords/Steps/AddKeyword', ["autoreplyId" => $autoreplyId]);
    }
    public function storeMessage(Request $request)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }
        $request->validate(
            [
                'msg' => 'required_if:files,null',
                'files' => 'required_without:msg',
                'autoreplyRule_id' => 'required'
            ],
            [],
            ['msg' => 'Message']
        );
        try {
            $camp_msg = new WaBotKeywordMsg();
            $camp_msg->body = $request->msg;
            $camp_msg->wa_bot_keyword_id = $request->autoreplyRule_id;
            if ($request->files->count() != 0) {
                foreach ($request->file('files') as $value) {
                    $fileName = time() . '-' . $value->getClientOriginalName();
                    $path = Storage::disk('public')->putFileAs(
                        'uploads/WaBotKeywordMsg',
                        $value,
                        $fileName,
                    );
                    $camp_msg->file = $path;
                }
            }
            $camp_msg->save();
            return redirect()->back();
        } catch (PDOException $e) {
            
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
        }
    }
    public function storeSteps(Request $request, $step = 1, $id = null)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }
        $rules = [
            1 => [
                "keyword" => "required|max:250",
            ],
            2 => []
        ];
        $request->validate(
            $rules[$step],
            [],
            [
                "keyword" => 'Keyword',
            ],
        );
        try {
            if ($id != null) {
                $autoReplyRule = WaBotKeyword::find($id);
            } else {
                $autoReplyRule = new WaBotKeyword();
            }

            
            if ($step == 1) {
                $autoReplyRule->keyword =
                    $request->keyword;
                $autoReplyRule->wa_bot_id =
                    $request->autoreply_id;
                $autoReplyRule->user_id = Auth::id();
                $autoReplyRule->save();
                return redirect(route('whatsapp.bot.keyword.create.step', ['step' => 2, 'id' => $autoReplyRule->id]));
            } elseif ($step == 2) {
                return redirect(route('whatsapp.bot.keyword.listKeyword', ["id" => $request->autoreplyId]));
            }
        } catch (Exception $e) {
            
        }
    }

    public function createSteps($step = 0, $id = null, $autoReplyId = null)
    {
        if (!request()->user()->can($this->tableName . ".add")) {
            abort(403, "unauthorized");
        }
        try {
            $data = [];
            $data['id'] = $id;
            if ($id != null) {
                $data['autoReplyRule'] = WaBotKeyword::where('id', $id)->where('user_id', Auth::id())->first();
                if ($data['autoReplyRule'] == null) {
                    return redirect(route('autoreply.keyword.listKeyword', ["id" => $autoReplyId]));
                }
            }
            switch ($step) {
                case 1:
                    $pagePath = "WhatsApp/Bot/Keywords/Steps/One";
                    break;
                case 2:
                    $pagePath = "WhatsApp/Bot/Keywords/Steps/Two";
                    break;
                default:
                    break;
            }
            return Inertia::render($pagePath, ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
    public function getBotKeywordMessages($id)
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        try {
            $data['messages'] = WaBotKeywordMsg::where('wa_bot_keyword_id', $id)->orderBy('id', 'desc')->get();
            $data['status'] = true;
            return response()->json($data);
        } catch (PDOException $e) {
            session()->flash('message', errorMessage(4));
            session()->flash("type", "failure");
            $data['status'] = false;
            $data['message'] = errorMessage(1);
            return response()->json($data);
        }
    }
    public function deleteKeywords($keywordIds)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }

        if ($keywordIds != null || $keywordIds != "") {
            try {
                $ids = explode(",", $keywordIds);
                WaBotKeyword::destroy($ids);
                return redirect()->back()->with(["message" => "Record(s) Deleted !!", "type" => "success"]);
            } catch (Exception $e) {
                if ($e->getCode() == 23000) {
                    return redirect()->back()->with(["message" => errorMessage(5), "type" => "failure"]);
                }
                return redirect()->back()->with(["message" => $e->getMessage(), "type" => "failure"]);
            }
        }
    }
    function deleteMessage($message)
    {
        if (!request()->user()->can($this->tableName . ".delete")) {
            abort(403, "unauthorized");
        }
        try {
            $msg = WaBotKeywordMsg::find($message);
            if ($msg) {
                if ($msg->file && Storage::disk('public')->exists($msg->file)) {
                    Storage::disk('public')->delete($msg->file);
                }
                $msg->delete();
            }
            session()->flash('message', 'Message Deleted.');
            session()->flash("type", "success");
            return redirect()->back();
            // return response()->json(['status' => true]);

        } catch (Exception $e) {
            if ($e->getCode() == 23000) {
                session()->flash('message', errorMessage(5));
                session()->flash("type", "failure");
                return response()->json(['status' => false]);
            }
            return response()->json(['status' => false]);
        }
    }
}
