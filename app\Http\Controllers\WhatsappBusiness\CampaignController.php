<?php

namespace App\Http\Controllers\WhatsappBusiness;

use App\Http\Controllers\Controller;

use DateTime;
use Exception;
use PDOException;
use Inertia\Inertia;
use App\Models\ContactList;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\WaBulkCampaignContactList;
use App\Models\WhatsappBusiness\WaBaGateway;
use App\Models\WhatsappBusiness\WaBaCampaign;
use App\Models\WhatsappBusiness\WaBaBulkCampaignMsg;
use App\Models\WhatsappBusiness\WaBaBulkCampaignGateway;
use App\Models\WhatsappBusiness\WaBaBulkCampaignVariable;
use App\Models\WhatsappBusiness\WaBaBulkCampaignContactList;

class CampaignController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaBaCampaign())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        try {
            $totalCount = $_GET['perPage'] ?? 10;
            $column = $_GET['column'] ?? 'id';
            $sortBy = $_GET['sort'] ?? 'desc';
            $userIds = request()->user()->getAllChildUserIds();
            $backData['campaigns'] = WaBaCampaign::orderBy($column, $sortBy)
                ->with('gateways', 'contactLists', 'user')
                ->accessibleByUser()
                ->paginate($totalCount)
                ->withQueryString()->toArray();
            $backData['getData'] = $_GET;
            $backData["can_add"] = request()->user()->can($this->tableName . ".add");
            $backData["can_edit"] = request()->user()->can($this->tableName . ".edit");
            $backData["can_delete"] = request()->user()->can($this->tableName . ".delete");
            return Inertia::render('WhatsappBusiness/Campaign/Index', ['collection' => $backData,]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */

    public function create()
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        return Inertia::render('WhatsappBusiness/Campaign/Steps/Add');
    }

    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }
        try {
            if (gettype($request->channels) != 'array') {
                $request['channels'] = json_decode($request->channels);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in channels'
                    ], 400);
                }
            }
            if (gettype($request->contactList) != 'array') {
                $request['contactList'] = json_decode($request->contactList);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in contactList'
                    ], 400);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                "status" => false,
                'message' => 'Invalid JSON in request.'
            ], 400);
        }

        $request->validate(
            [
                "campaignName" => "required|min:3|max:50",
                "channels" => "array|required",
                "contactList" => "array|required|max:5",
                "isFooterTimeEnable" => "required",
                "viewOnce" => "required",
                "startDate" => "required|date",
                "startTime" => "required",
                "enableEndTime" => "required",
                "stopDate" => "required_if:enableEndTime,true",
                "stopTime" => "required_if:enableEndTime,true",
                "sleepAfterMsgs" => "required|numeric|min:1",
                "sleepForSeconds" => "required|numeric|min:1",
            ],
            [],
            [
                "campaignName" => 'Campaign Name',
                "message" => 'Message',
                "isFooterTimeEnable" => 'Footer Time Enable',
                "viewOnce" => 'View Once',
                "sleepFor" => 'Sleep for',
                "sleepAfter" => 'Sleep After',
                "channels" => 'Channel',
                "startTime" => 'Start Time',
                "contactList" => 'Contact List',
                "mediaFiles" => 'Media',
            ],
        );
        DB::beginTransaction();
        try {
            $contactLists = ContactList::whereIn('id', $request->contactList)->withCount('contacts')->get();
            $Campaign = new WaBaCampaign();
            $Campaign->name = $request->campaignName;
            $Campaign->msgFooterDateTime = $request->isFooterTimeEnable;
            $Campaign->viewOnce = $request->viewOnce;
            $Campaign->sleepAfterMsgs = $request->sleepAfterMsgs;
            $Campaign->sleepForSeconds = $request->sleepForSeconds;
            $Campaign->user_id = Auth::id();
            if ($request->has(["startDate", "startTime"])) {
                $formatedStopDateTime = (new DateTime("{$request->startDate} {$request->startTime}"))->format('Y-m-d H:i:s');
                $Campaign->startTime = $formatedStopDateTime;
            }

            if ($request->enableEndTime == true) {
                if ($request->has(["stopDate", "stopTime"])) {
                    $formatedStopDateTime = (new DateTime("{$request->stopDate} {$request->stopTime}"))->format('Y-m-d H:i:s');
                    $Campaign->endTime = $formatedStopDateTime;
                }
            }

            $Campaign->totalContacts = $contactLists->sum('contacts_count'); // need contact list contacts

            $Campaign->save();
            $bulkContactListForCampaign = [];
            foreach ($request->contactList as $k => $cl) {
                $bulkContactListForCampaign[] = [
                    'waba_campaign_id' => $Campaign->id,
                    'contact_list_id' => $cl,
                    'user_id' => Auth::id(),
                ];
            }
            $bulkChannelsForCampaign = [];
            
            foreach ($request->channels as $ch) {
                $bulkChannelsForCampaign[] = [
                    'waba_campaign_id' => $Campaign->id,
                    'waba_gateway_id' => $ch,
                    'user_id' => Auth::id(),
                ];
            }
            $Campaign->channels()->createManyQuietly(
                $bulkChannelsForCampaign
            );
            $Campaign->contactLists()->createManyQuietly(
                $bulkContactListForCampaign
            );
            DB::commit();

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign Created.", 'status' => true])
                : redirect()->route('whatsappB.campaign.create.message', ['campaign' => $Campaign->id, "mode" => "create"])->with(["message" => "Campaign Created.", "type" => "success"]);
        } catch (PDOException $e) {
            DB::rollback();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $e->errorInfo[2], 'status' => false])
                : redirect()->back()->with(["message" => $e->errorInfo[2], "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (!request()->user()->can($this->tableName . '.view')) {
            abort(403, "unauthorized");
        }
        try {
            $backData['campaign'] = WaBaCampaign::where('id', $id)
                ->with('channels', 'contactLists', 'user', 'pivotChannels', 'template', "gateways")
                ->AccessibleByUser()
                ->withCount('failed', 'sent', 'nonWhatsapp')
                ->first();

            $backData['pluckIds'] = $backData['campaign']->channels()->pluck('waba_gateway_id', 'waba_gateway_id');
            $backData['channels'] = WaBaGateway::where('user_id', Auth::id())->get();
            $backData['attachments'] = [];
            $attachArray = explode(',', $backData['campaign']->wa_media);
            $backData['attachments_count'] = count($attachArray);
            $backData['id'] = $id;
            $allChannelIds = array_flip(array_column($backData["campaign"]->pivotChannels->toArray(), 'id'));
            $backData["remainingGateways"] = array_values(array_filter($backData["channels"]->toArray(), function ($record) use ($allChannelIds) {
                return !isset($allChannelIds[$record['id']]);
            }));
            return response()->json(['data' => $backData, 'status' => true]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ? response()->json(['message' => errorMessage(3), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage(3), "type" => "failure"]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $campaign)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }
        try {
            $data['id'] = $campaign;
            $data['campaign'] = WaBaCampaign::with('pivotChannels', "pivotContactLists")->where('id', $campaign)->where('user_id', Auth::id())->first();
            if ($data['campaign'] == null) {
                return redirect(route('whatsappB.campaign.index'))->with(["message" => "Campaign not found.", "type" => "failure"]);
            }
            $data['channels'] = $data['campaign']->channels()->pluck('waba_gateway_id');
            $data['contactLists'] = $data['campaign']->contactLists()->pluck('contact_list_id');
            return Inertia::render('WhatsappBusiness/Campaign/Steps/Edit', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json')
                ? response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }
        try {
            if (gettype($request->channels) != 'array') {
                $request['channels'] = json_decode($request->channels);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in channels'
                    ], 400);
                }
            }
            if (gettype($request->contactList) != 'array') {
                $request['contactList'] = json_decode($request->contactList);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        "status" => false,
                        'message' => 'Invalid JSON in contactList'
                    ], 400);
                }
            }
        } catch (Exception $e) {
            return response()->json([
                "status" => false,
                'message' => 'Invalid JSON in request.'
            ], 400);
        }

        $request->validate(
            [
                "campaignName" => "required|min:3|max:50",
                "channels" => "array|required",
                "contactList" => "array|required|max:5",
                "isFooterTimeEnable" => "required",
                "viewOnce" => "required",
                "startDate" => "required|date",
                "startTime" => "required",
                "enableEndTime" => "required",
                "stopDate" => "required_if:enableEndTime,true",
                "stopTime" => "required_if:enableEndTime,true",
                "sleepAfterMsgs" => "required|numeric|min:1",
                "sleepForSeconds" => "required|numeric|min:1",
            ],
            [],
            [
                "campaignName" => 'Campaign Name',
                "message" => 'Message',
                "isFooterTimeEnable" => 'Footer Time Enable',
                "viewOnce" => 'View Once',
                "sleepFor" => 'Sleep for',
                "sleepAfter" => 'Sleep After',
                "channels" => 'Channel',
                "startTime" => 'Start Time',
                "contactList" => 'Contact List',
                "mediaFiles" => 'Media',
            ],
        );

        DB::beginTransaction();
        try {
            $contactLists = ContactList::whereIn('id', $request->contactList)->withCount('contacts')->get();
            $Campaign = WaBaCampaign::find($id);
            $Campaign->name = $request->campaignName;
            $Campaign->msgFooterDateTime = $request->isFooterTimeEnable;
            $Campaign->viewOnce = $request->viewOnce;
            $Campaign->sleepAfterMsgs = $request->sleepAfterMsgs;
            $Campaign->sleepForSeconds = $request->sleepForSeconds;
            $Campaign->user_id = Auth::id();
            if ($request->has(["startDate", "startTime"])) {
                $formatedStopDateTime = (new DateTime("{$request->startDate} {$request->startTime}"))->format('Y-m-d H:i:s');
                $Campaign->startTime = $formatedStopDateTime;
            }

            if ($request->enableEndTime == true) {
                if ($request->has(["stopDate", "stopTime"])) {
                    $formatedStopDateTime = (new DateTime("{$request->stopDate} {$request->stopTime}"))->format('Y-m-d H:i:s');
                    $Campaign->endTime = $formatedStopDateTime;
                }
            } else {
                $Campaign->endTime = null;
            }

            $Campaign->totalContacts = $contactLists->sum('contacts_count'); // need contact list contacts

            $Campaign->save();
            $bulkContactListForCampaign = [];
            //delete old contact list
            WaBaBulkCampaignContactList::where('waba_campaign_id', $id)->delete();
            foreach ($request->contactList as $k => $cl) {
                $bulkContactListForCampaign[] = [
                    'waba_campaign_id' => $Campaign->id,
                    'contact_list_id' => $cl,
                    'user_id' => Auth::id(),
                ];
            }
            $bulkChannelsForCampaign = [];
            // delete old channels
            WaBaBulkCampaignGateway::where('waba_campaign_id', $id)->delete();
            foreach ($request->channels as $ch) {
                $bulkChannelsForCampaign[] = [
                    'waba_campaign_id' => $Campaign->id,
                    'waba_gateway_id' => $ch,
                    'user_id' => Auth::id(),
                ];
            }
            $Campaign->channels()->createManyQuietly(
                $bulkChannelsForCampaign
            );
            $Campaign->contactLists()->createManyQuietly(
                $bulkContactListForCampaign
            );

            DB::commit();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign Updated.", 'status' => true])
                : redirect()->route('whatsappB.campaign.create.message', ['campaign' => $Campaign->id, "mode" => "edit"])->with(["message" => "Campaign Updated.", "type" => "success"]);
        } catch (PDOException) {
            DB::rollback();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Fail to update Campaign.", 'status' => false])
                : redirect()->back()->with(["message" => "Fail to update Campaign.", "type" => "failure"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }
        try {
            $ids = explode(",", $id);
            $campaigns = WaBaCampaign::whereIn('id', $ids)->get();
            foreach ($campaigns as $campaign) {
                if ($campaign->user_id != Auth::id()) {
                    return (request()->header('Accept') == 'application/json') ?
                        response()->json(['message' => 'You are not authorized to delete this campaign.', 'status' => false])
                        : redirect()->back()->with(["message" => 'You are not authorized to delete this campaign.', "type" => "failure"]);
                }
                if ($campaign->wa_media) {
                    $media = explode(',', $campaign->wa_media);
                    foreach ($media as $m) {
                        if (Storage::disk('public')->exists($m)) {
                            Storage::disk('public')->delete($m);
                        }
                    }
                }
            }
            if ($campaigns->count() > 0) {
                WaBaCampaign::whereIn('id', $ids)->where('user_id', Auth::id())->delete();
                session()->flash('message', 'Campaign Deleted.');
                session()->flash("type", "success");
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Campaign Deleted.', 'status' => true])
                    : redirect()->back()->with(["message" => 'Campaign Deleted.', "type" => "success"]);
            } else {
                return (request()->header('Accept') == 'application/json') ?
                    response()->json(['message' => 'Campaign not found.', 'status' => false])
                    : redirect()->back()->with(["message" => 'Campaign not found.', "type" => "failure"]);
            }
        } catch (PDOException $e) {
            $message = errorMessage($e->getCode());
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => $message, 'status' => false])
                : redirect()->back()->with(["message" => $message, "type" => "failure"]);
        }
    }

    public function campaignUpdateAddChannels(Request $r)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        DB::beginTransaction();
        try {
            $bulkChannelsForCampaign = [];
            foreach ($r->channels as $ch) {
                $array = [
                    'waba_campaign_id' => $r->id,
                    'waba_gateway_id' => $ch,
                    'user_id' => Auth::id(),
                ];
                if (WaBaBulkCampaignGateway::where($array)->exists() == false) {
                    $bulkChannelsForCampaign[] = $array;
                }
            }
            WaBaBulkCampaignGateway::insert($bulkChannelsForCampaign);
            DB::commit();
            session()->flash('message', 'Campaign Updated.');
            session()->flash("type", "success");
        } catch (PDOException $e) {
            DB::rollback();
            session()->flash('message', errorMessage());
            session()->flash("type", "failure");
            return false;
        }
    }

    public function campaignUpdateAddChannel(Request $r)
    {
        if (!request()->user()->can($this->tableName . '.edit')) {
            abort(403, "unauthorized");
        }

        try {
            $array = [
                'waba_campaign_id' => $r->data['campaign'],
                'waba_gateway_id' => $r->data['gateway'],
                'user_id' => Auth::id(),
            ];
            if (WaBaBulkCampaignGateway::where($array)->exists()) {

                $flashData["type"] = 'failure';
                $flashData["message"] = 'Channel already exist.';
            } else {
                WaBaBulkCampaignGateway::insert($array);
                $flashData["type"] = 'success';
                $flashData["message"] = 'Campaign Updated.';
            }
            session()->flash('message', $flashData["message"]);
            session()->flash("type", $flashData["type"]);
            return;
        } catch (PDOException $e) {
            
            // DB::rollback();
            session()->flash('message', errorMessage());
            session()->flash("type", "failure");
            return false;
        }
    }

    public function campaignUpdateAddContactList(Request $r)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        DB::beginTransaction();
        try {

            $bulkChannelsForCampaign = [];
            foreach ($r->channels as $ch) {
                $array = [
                    'wa_campaign_id' => $r->id,
                    'contact_list_id' => $ch,
                    'user_id' => Auth::id(),
                ];
                if (WaBulkCampaignContactList::where($array)->exists() == false) {
                    $bulkChannelsForCampaign[] = $array;
                }
            }
            WaBulkCampaignContactList::insert($bulkChannelsForCampaign);
            DB::commit();
            session()->flash('message', 'Campaign Updated.');
            session()->flash("type", "success");
        } catch (PDOException $e) {
            DB::rollback();
            session()->flash('message', $e->errorInfo[2]);
            session()->flash("type", "failure");
            return false;
        }
    }

    public function createMessage($campaign, $mode)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        if (!$campaign) {
            return redirect(route('whatsappB.campaign.index'));
        }

        try {
            $data = ['id' => $campaign];

            if ($mode == "edit") {
                $data["campaign"] = WaBaCampaign::query()
                    ->with("template:templateJson", "templateVariables")
                    ->where('id', $campaign)
                    ->accessibleByUser()
                    ->first();
            }

            $viewName = $mode === "edit" ? "EditMessage" : "AddMessage";
            return Inertia::render("WhatsappBusiness/Campaign/Steps/{$viewName}", [
                'collection' => $data
            ]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => true])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    public function storeMessage(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            'templateId' => ['required'],
            'totalVariableCount' => ['required', 'array'],
            'totalVariableCount.Header' => ['required'],
            'totalVariableCount.Body' => ['required'],
            'varObject' => ['array', function ($attribute, $value, $fail) use ($request) {
                $headerVariableCount = $request->input('totalVariableCount.Header', 0);
                $bodyVariableCount = $request->input('totalVariableCount.Body', 0);
                if ($headerVariableCount > 0) {
                    if (empty($value['Header']) || count($value['Header']) != $headerVariableCount) {
                        $fail('Please fill all Header variables.');
                    }
                }

                if ($bodyVariableCount > 0) {
                    if (empty($value['Body']) || count($value['Body']) != $bodyVariableCount) {
                        $fail('Please fill all Body variables.');
                    }
                }
            }],
            'varObject.Header.*.column' => ['nullable'],
            'varObject.Header.*.fixed' => ['nullable'],
            'varObject.Body.*' => ['array'],
            'varObject.Body.*.column' => ['nullable'],
            'varObject.Body.*.fixed' => ['nullable'],
        ], [
            'templateId.required' => 'Please Select Atleast One Template'
        ]);

        try {
            $campaignVariablesData = [];
            foreach ($request->varObject as $variableFor => $variables) {
                foreach ($variables as $key => $variable) {
                    $variableData = [
                        'waba_campaign_id' => $request->campaignId,
                        'waba_template_id' => $request->templateId,
                        'variableFor' => strtoupper($variableFor),
                        'key' => $key,
                        'valueFixed' => ($key == "image") ? null : $variable['fixed'],
                        'valueField' => ($key == "image") ? null : $variable['column'],
                    ];

                    if ($key == "image" && $request->hasFile('varObject.' . $variableFor . '.image')) {
                        $file = $request->file('varObject.' . $variableFor . '.image');
                        $fileName = time() . '-' . $file->getClientOriginalName();
                        $path = Storage::disk('public')->putFileAs(
                            'uploads/WaBaBulkCampaignVariables',
                            $file,
                            $fileName
                        );
                        $variableData['url'] = 'storage/' . $path;
                    }

                    $campaignVariablesData[] = $variableData;
                }
            }

            if (empty($campaignVariablesData)) {
                $campaignVariablesData = [
                    [
                        'waba_campaign_id' => $request->campaignId,
                        'waba_template_id' => $request->templateId,
                    ]
                ];
            }

            WaBaBulkCampaignVariable::insert($campaignVariablesData);

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign variables stored successfully", 'status' => true])
                : redirect()->route('whatsappB.campaign.index')->with(["message" => "Campaign variables stored successfully", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }
    public function editMessage(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            'templateId' => ['required'],
            'totalVariableCount' => ['required', 'array'],
            'totalVariableCount.Header' => ['required'],
            'totalVariableCount.Body' => ['required'],
            'varObject' => ['array', function ($attribute, $value, $fail) use ($request) {
                $headerVariableCount = $request->input('totalVariableCount.Header', 0);
                $bodyVariableCount = $request->input('totalVariableCount.Body', 0);
                if ($headerVariableCount > 0) {
                    if (empty($value['Header']) || count($value['Header']) != $headerVariableCount) {
                        $fail('Please fill all Header variables.');
                    }
                }

                if ($bodyVariableCount > 0) {
                    if (empty($value['Body']) || count($value['Body']) != $bodyVariableCount) {
                        $fail('Please fill all Body variables.');
                    }
                }
            }],
            'varObject.Header.*.column' => ['nullable'],
            'varObject.Header.*.fixed' => ['nullable'],
            'varObject.Body.*' => ['array'],
            'varObject.Body.*.column' => ['nullable'],
            'varObject.Body.*.fixed' => ['nullable'],
        ], [
            'templateId.required' => 'Please Select Atleast One Template'
        ]);
        try {
            $existingRecords = WaBaBulkCampaignVariable::where('waba_campaign_id', $request->campaignId)->exists();
            if ($existingRecords) {
                WaBaBulkCampaignVariable::where('waba_campaign_id', $request->campaignId)->delete();
            }
            $campaignVariablesData = [];
            foreach ($request->varObject as $variableFor => $variables) {
                foreach ($variables as $key => $variable) {
                    $variableData = [
                        'waba_campaign_id' => $request->campaignId,
                        'waba_template_id' => $request->templateId,
                        'variableFor' => strtoupper($variableFor),
                        'key' => $key,
                        'valueFixed' => ($key == "image") ? null : $variable['fixed'],
                        'valueField' => ($key == "image") ? null : $variable['column'],
                    ];

                    if ($key == "image" && $request->hasFile('varObject.' . $variableFor . '.image')) {
                        $file = $request->file('varObject.' . $variableFor . '.image');
                        $fileName = time() . '-' . $file->getClientOriginalName();
                        $path = Storage::disk('public')->putFileAs(
                            'uploads/WaBaBulkCampaignVariables',
                            $file,
                            $fileName
                        );
                        $variableData['url'] = 'storage/' . $path;
                    }

                    $campaignVariablesData[] = $variableData;
                }
            }

            if (empty($campaignVariablesData)) {
                $campaignVariablesData = [
                    [
                        'waba_campaign_id' => $request->campaignId,
                        'waba_template_id' => $request->templateId,
                    ]
                ];
            }

            WaBaBulkCampaignVariable::insert($campaignVariablesData);

            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => "Campaign variables stored successfully", 'status' => true])
                : redirect()->route('whatsappB.campaign.index')->with(["message" => "Campaign variables stored successfully", "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    function deleteMessage($message)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }

        $wantJson = request()->header('Accept') == 'application/json';
        try {
            $msg = WaBaBulkCampaignMsg::find($message);
            if (!$msg) {
                return $wantJson ?
                    response()->json(['message' => "Message not found.", 'status' => false])
                    : redirect()->back()->with(['message' => "Message not found.", "type" => "info"]);
            }
            if ($msg->campaign->user_id !== Auth::id()) {
                return $wantJson ?
                    response()->json(['message' => "Message not found.", 'status' => false])
                    : redirect()->back()->with(['message' => "Unauthorized action.", "type" => "success"]);
            }

            if ($msg->file && $msg->isTemplateFile == 0 && Storage::disk('public')->exists($msg->file)) {
                Storage::disk('public')->delete($msg->file);
            }
            $msg->delete();
            session()->flash('message', "Message Deleted.");
            session()->flash("type", "success");
            return $wantJson ?
                response()->json(['message' => "Message Deleted.", 'status' => true])
                : redirect()->back();
        } catch (Exception $e) {
            if ($wantJson) {
                response()->json(['message' => errorMessage(), 'status' => false]);
            }
            if ($e->getCode() == 23000) {
                return redirect()->back()->with(['message' => errorMessage(5), "type" => "failure"]);
            }
            return redirect()->back()->with(['message' => errorMessage(5), "type" => "failure"]);
        }
    }
}
