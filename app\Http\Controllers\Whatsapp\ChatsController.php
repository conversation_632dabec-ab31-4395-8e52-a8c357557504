<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\WaBot;
use App\Models\WaManualSending;
use App\Models\WaMsgs;
use App\Models\WaTemplate;
use Carbon\Carbon;
use Exception;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

use function Laravel\Prompts\select;

class ChatsController extends Controller
{
    private $tableName;
    public function __construct()
    {
        $this->tableName = (new WaManualSending())->getTable();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        return Inertia::render('WhatsApp/Chats/Index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
                $request->validate(
            [
                'message' => 'bail|required_without:attachment',
                'attachment.*' => 'required_without:message|file|max:20000',
                "sendTo" => "required",
            ],
            [],
            ['attachment' => 'File', 'message' => 'Message', 'attachment.*' => 'File']
        );

        try {
            $message = new WaManualSending();
            $message->mobile = explode('@', $request->sendTo)[0];
            $message->messageDateTime = $request->isScheduled ? new \DateTime($request->ScheduleTime) : Carbon::now();
            $message->user_id = Auth::id();

            $message->wa_gateway_id = $request->gateway;
            $message->body = $request->message;
            if ($request->hasFile('attachment')) {

                foreach ($request->attachment as $key => $value) {
                    if (isset($value)) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = storeFile('uploads/' . $this->tableName, $value, $fileName);
                        $message->file = $path ?? "";
                    }
                }
            }
            $message->save();
            return redirect()->back()->with(["chatMsg" => "Chat Message Added", "type" => "success"]);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'msg' => errorMessage()]);
        }
    }

    /*
         user_id, wa_gateway_id, mobile, body, file, status, wapiResponse, datetime
    */

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $data['messages1'] = $messages1 = WaManualSending::where('mobile', $id)->with('channel')->orderBy('messageDateTime', 'desc')->paginate(10)->toArray();
            $messages1_data = collect($messages1['data']);


            $data['messages2'] = $messages2 = WaMsgs::where('remoteJid', 'like', "%$id%")->with('channel')->orderBy('messageDateTime', 'desc')->paginate(10)->toArray();
                        $messages2_data = collect($messages2['data']);
            $chats = $messages2_data->merge($messages1_data)->map(function ($item) use ($messages1_data) {
                if (isset($item['remoteJid'])) {
                    $item['tableName'] = 'WaMsgs'; // Replace with your actual key and value
                } else {
                    $item['tableName'] = 'WaManualSending';
                }
                return $item;
            });

            return response()->json(['data' => $chats, 'collection' => $data, 'id' => $id]);
        } catch (Exception $e) {
                        return response()->json(['data' => [], 'status' => false]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function deleteScheduled(Request $req)
    {
                try {

            $table = match ($req->table) {
                'WaMsgs' => 'wa_msgs',
                'WaManualSending' => 'wa_manual_sending',
                default => null
            };
            if ($table) {
                $delete = DB::table($table)->where(['id' => $req->message, 'user_id' => Auth::id()])->delete();
            } else {
                return response()->json(['status', false]);
            }
            // return redirect()->back()->with()
            return response()->json(['status', $delete]);
        } catch (Exception $e) {
            if ($e->getCode() == 23000) {
                session()->flash('message', errorMessage(5));
                session()->flash("type", "failure");
                return redirect()->back();
            }
            return response()->json(['status', false]);
        }
    }
    function sendTemplate(Request $request)
    {
        if (($request->varObject && count($request->varObject)) != $request->variable_count) {
            $errorMsgs['variable_error'] = "Template variable required.";
            return redirect()->back()->withErrors($errorMsgs);
        }

        try {

            $template = WaTemplate::find($request->template_id);

            if ($template) {
                $templateBody = $template->body;
                if ($request->varObject) {
                    foreach ($request->varObject as $key => $varValue) {
                        if ($varValue == null || empty($varValue)) {
                            $errorMsgs['variable_error'] = "Template variable required.";
                            return redirect()->back()->withErrors($errorMsgs);
                        }
                        $templateBody = str_replace(['[[', ']]', $key], ['', '', $varValue], $templateBody);
                    }
                }

                $message = new WaManualSending();
                $message->body = $templateBody;
                $message->mobile = explode('@', $request->sendTo)[0];
                $message->user_id = Auth::id();
                $message->wa_gateway_id = $request->gateway;
                $message->messageDateTime = Carbon::now();
                if (count($request->attachment) == 0) {
                    if ($request->template_file && !empty($request->template_file)) {
                        $message->file = "/storage/$request->template_file";
                    }
                } else {
                    foreach ($request->attachment as $key => $value) {
                        $fileName = time() . '-' . $value->getClientOriginalName();
                        $path = Storage::disk('public')->putFileAs(
                            'uploads/waManualSending',
                            $value,
                            $fileName,
                        );
                        $message->file = $path;
                    }
                }
                $message->save();
                return redirect()->back();
            } else {
                session()->flash('message', 'Something went wrong.');
                session()->flash("type", "failure");
                return redirect()->back();
            }
        } catch (Exception $e) {
                    }

            }

    function sendNowScheduled(Request $req)
    {
        try {
            $message = 'Message Updated.';
            $status = true;

            $record = WaManualSending::where(['user_id' => Auth::id(), 'id' => $req->message])->first();
            if ($record) {
                if ($record->messageDateTime > Carbon::now()) {
                    $record->messageDateTime = Carbon::now();
                    $record->update();
                } else {
                    $message = 'Invalid request.';
                    $status = false;
                }
            } else {
                $message = 'Parameter not match.';
                $status = false;
            }
            return response()->json(['message' => $message, 'status' => $status]);
        } catch (Exception $e) {
            $message = errorMessage(1);
            $status = false;
            return response()->json(['message' => $message, 'status' => $status]);
        }
    }
}
