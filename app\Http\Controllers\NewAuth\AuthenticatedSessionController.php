<?php

namespace App\Http\Controllers\NewAuth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;


class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create()
    {
        if (Auth::check()) {
            return redirect()->route('dashboard');
        }


        $loginView = 'Auth/Login';
        $viewData = [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ];

        return Inertia::render($loginView, $viewData);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(Request $request)
    {
        $request->validate([
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
        ]);
        try {
            $user = User::where('username', $request->username)->first();
            if ($user) {
                if ($user->password == $request->password) {

                    if (Auth::loginUsingId($user->id)) {

                        return (request()->header('Accept') == 'application/json')
                            ?
                            response()->json(["message" => "Login Success", "accessToken" => $user->createToken($user->username)->plainTextToken, "status" => true])
                            :
                            redirect('/');
                    }
                } else {
                    return (request()->header('Accept') == 'application/json')
                        ?
                        response()->json(['message' => 'Invalid Password.', 'status' => false])
                        :
                        redirect()->back()->with(['message' => 'Invalid Password.', 'type' => 'error'])->withInput();
                }
            } else {
                if ($request->acceptsJson()) {
                    return response()->json(['message' => 'Username is incorrect.', 'status' => false]);
                }
                return redirect()->back()->with(['message' => 'Username is incorrect.', 'type' => 'error'])->withInput();
            }
            if ($request->acceptsJson()) {
                return response()->json(["message" => "Login Success", "access Token" => $user->createToken($user->username)->plainTextToken, "status" => true]);
            }
            return redirect()->intended(route('dashboard', absolute: false));
        } catch (Exception $e) {
           return (request()->header('Accept') == 'application/json') ?
                response()->json(["message" => $e->getMessage(), "status" => false]) :
                redirect()->back()->with(["message" => $e->getMessage(), "type" => 'failure']);
        }
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request)
    {
       if (request()->header('Accept') == 'application/json') {
            $success = $request->user()->currentAccessToken()->delete();
            return response()->json(["message" => $success ? "Logout Success" : "Logout Failed", "status" => $success]);
        }
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();
        $subdomain = $this->normalizeSubdomain(request()->getHost());
        Cache::forget("subdomain_config_{$subdomain}");

        return redirect('/');
    }
    protected function normalizeSubdomain($host): string
    {
        return in_array(($subdomain = explode('.', $host)[0]), ['www', 'localhost']) ? 'localhost' : $subdomain;
    }
}
