<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;

if (!function_exists('mobileFilter')) {
    function mobileFilter($numbers)
    {
        $numbers = preg_replace('/\n+/', ',', trim($numbers)); //replaced all LINE BREAKS and MULTIPLE SPACES into ONE SPACE
        $numArr = explode(",", $numbers);
        $filteredNumbersArray = [];
        foreach ($numArr as $key => $value) {
            $numAdd = trim(preg_replace('/\D/', '', $value));
            array_push($filteredNumbersArray, $numAdd);
        }
        return $filteredNumbersArray;
    }
}

if (!function_exists('errorMessage')) {
    function errorMessage($number = 1)
    {
        $messages = [
            1 => 'We apologize, but we are unable to process your request at this time.',
            2 => 'A system error has occurred. Please try again later.',
            3 => 'We encountered an issue while processing your request. Please try again.',
            4 => 'We were unable to retrieve the requested record. Please verify and try again.',
            190 => 'Your API session has expired. Please log in again to continue.',
            5 => "This item cannot be deleted as it is associated with other records. Please remove the linked records first.",
            '23000' => "This item cannot be deleted due to existing dependencies. Please remove associated records before proceeding.",
            '1451' => "Deletion is not possible due to foreign key constraints. Please remove related records first.",
            '22001' => "The data provided exceeds the maximum allowed length for this field.",
            '1364' => "A required field is missing. Please review the form and provide all necessary information.",
            '1136' => 'The data format is invalid or required fields are missing. Please check your input and try again.',
            '1406' => "The entered data exceeds the maximum character limit. Please shorten your input and try again.",
            '1146' => 'A critical system error has occurred. Please contact the administrator for assistance.',
            '42S22' => 'The specified column name is invalid or does not exist in the database schema.',
            '1062' => 'A duplicate entry was detected. Please ensure the data is unique and try again.',
            '1054' => 'An unknown column was referenced in the query. Please check the field names and try again.',
            '1452' => 'A foreign key constraint has failed. Please ensure the referenced data exists.',
            '1064' => 'There is a syntax error in your SQL statement. Please review and correct the query.',
            '1213' => 'A deadlock has occurred. Please try your transaction again.',
            '1045' => 'Access denied. Please check your credentials and permissions.',
            '1644'=>'Your limit of Maximum Active Gateways is reached.',
        ];

        return $messages[$number] ?? 'Unable to proceed Request';
    }
}

if (!function_exists('removeTextAfterAT')) {
    function removeTextAfterAT($string)
    {
        $atPosition = strpos($string, '@');
        $parts = explode('@', $string);

        // Get the first element (username)
        return $parts[0];
    }
}

if (!function_exists('validateEmails')) {
    function validateEmails(array $emails): array
    {
        $validEmails = [];
        $invalidEmails = [];

        foreach ($emails as $email) {
            // Trim to remove extra spaces
            $email = trim($email);

            // Validate email format
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $validEmails[] = $email;
            } else {
                $invalidEmails[] = $email;
            }
        }

        return [
            'valid' => $validEmails,
            'invalid' => $invalidEmails
        ];
    }
}

if (!function_exists('storeFile')) {
    function storeFile($storeAt, $file, $fileName = null)
    {
        try {

            if ($fileName == null) {
                $fileName = $file->getClientOriginalName();
            }
            return Storage::disk('public')->putFileAs(
                $storeAt,
                $file,
                $fileName,
            );
        } catch (Exception $e) {

            return $e;
        }
    }
}

if (!function_exists('getStoreFile')) {
    function getStoreFile($path = null)
    {
        try {
            return Storage::disk('public')->get($path);
        } catch (Exception) {
            return null;
        }
    }
}

if (!function_exists('deleteFile')) {
    function deleteFile($filePath)
    {
        try {
            if ($filePath) {

                if (Storage::disk('public')->exists($filePath)) {
                    Storage::disk('public')->delete($filePath);
                }
            }
            return true;
        } catch (Exception) {
            Log::alert('Error deleting file: ' . $filePath);
            return false;
        }
    }
}

if (!function_exists('menuCounts')) {
    function menuCounts($routeName = null)
    {
        // If no route name is provided, get the current route name
        if (!$routeName) {
            $routeName = Route::currentRouteName();
        }

        // Determine the database table based on the route name
        $table = match (true) {
            str_starts_with($routeName, 'whatsappB.') => 'waba_counts',
            str_starts_with($routeName, 'whatsapp.') => 'wa_counts',
            str_starts_with($routeName, 'mail.') => 'mail_counts',
            str_starts_with($routeName, 'sms2.') => 'sms_counts',

            default => null,
        };

        // If a table is found, fetch the data for the authenticated user
        return $table ? DB::table($table)->where('user_id', Auth::id())->first() : null;
    }
}
if (!function_exists('isMasterDomain')) {
    function isMasterDomain()
    {
        return in_array(request()->getHttpHost(), ["clatos.com", "democlatos.orkia.in", "127.0.0.1:8002","localhost:8002"]);
    }
}
