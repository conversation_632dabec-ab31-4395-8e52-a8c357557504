import { FilePreview } from "@/Components/FilePreview";
import MessageBox from "@/Components/HelperComponents/MessageBox";
import TemplateBox from "@/Components/TemplateBox";
import { input_custom } from "@/Pages/Helpers/DesignHelper";
import { textFormatting } from "@/Pages/Helpers/Helper";
import { router, useForm } from "@inertiajs/react";
import { Button, Label, Select, TextInput } from "flowbite-react";
import { useEffect, useState } from "react";
import { RiCheckDoubleFill } from "react-icons/ri";

export default function Edit({
    id,
    onClose
}) {
    const [image, setImage] = useState("");
    const [oldData, setOldData] = useState(null);

    const { data, setData, post, processing, reset, errors } = useForm({
        templateId: "",
        msg: "",
        templateName: "",
        category_id: "",
        files: {},
        attachment: {}
    });

    function fetchtemplateData(id) {
        return fetch(
            route("whatsapp.templates.edit", {
                template: id,
            })
        )
            .then((res) => {
                return res.json();
            })
            .then((result) => {
                setOldData(result);
            })
            .catch((error) => {
                console.error("Error fetching badge data:", error);
            });
    }

    useEffect(() => {
        fetchtemplateData(id);
    }, []);
    useEffect(() => {
        setData('message', data.msg);
    }, [data.msg]);

    useEffect(() => {
        setData("attachment", data.files);
    }, [data.files]);

    useEffect(() => {
        if (oldData != null) {
            setData({
                msg: oldData.templates.body ?? "",
                templateName: oldData.templates.name ?? "",
                category_id: oldData.templates.wa_template_category_id ?? "",
                files: {},
                templateId: oldData.templates.id,
            });
            setImage(oldData.templates.file);
        }
    }, [oldData]);

    function handleTemplateForm(e) {
        e.preventDefault();
        post(
            route("whatsapp.templates.update", {
                template: id,
            }),
            {
                onSuccess: () => {
                    reset();
                    onClose(true);
                    router.get(route("whatsapp.templates.index"));
                },
            }
        );
    }
    return (
        <form onSubmit={handleTemplateForm}>
            <div className="">
                <div className="grid items-start grid-flow-row-dense grid-cols-12 gap-3">
                    <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-8 lg:col-span-8 md:col-span-12 col-span-full">
                        <div className="flex flex-col gap-3 p-2 px-3 bg-white rounded-lg lg:flex-row">
                            <div className="w-full">
                                <div className="block mb-2">
                                    <Label
                                        htmlFor="templateName"
                                        value="Template Name"
                                    />
                                </div>
                                <TextInput
                                    theme={input_custom}
                                    id="templateName"
                                    type="text"
                                    required
                                    value={data.templateName}
                                    onChange={(e) =>
                                        setData("templateName", e.target.value)
                                    }
                                    placeholder="E.g. Welcome New Customers"
                                    color={
                                        errors.templateName ? "failure" : "gray"
                                    }
                                    helperText={
                                        errors.templateName &&
                                        errors.templateName
                                    }
                                />
                            </div>
                            <div className="w-full">
                                <div className="block mb-2">
                                    <Label
                                        htmlFor="categories"
                                        value="Category"
                                    />
                                </div>

                                <Select
                                    id="categories"
                                    required
                                    onChange={(e) =>
                                        setData("category_id", e.target.value)
                                    }
                                    color={
                                        errors.category_id ? "failure" : "gray"
                                    }
                                    helperText={
                                        errors.category_id && errors.category_id
                                    }
                                >
                                    <option disabled value={""}>
                                        Select Category
                                    </option>
                                    {oldData &&
                                        oldData.categories.map(
                                            (category, key) => {
                                                return (
                                                    <option
                                                        value={category.id}
                                                        selected={
                                                            oldData.templates
                                                                .wa_template_category_id ==
                                                            category.id
                                                        }
                                                    >
                                                        {category.name}
                                                    </option>
                                                );
                                            }
                                        )}
                                </Select>
                            </div>
                        </div>
                        <div className="p-2 bg-white rounded-md">
                            <div className="mb-2 text-sm font-medium">
                                Compose Message
                            </div>
                            <div className="bg-[#F0EEED] p-2">
                                <MessageBox data={data.msg} allData={data} errors={errors} setData={setData} addVariables={true} fileUpload={true} isOfficial />
                            </div>
                        </div>
                        <div className="mt-2">
                            <Button
                                className="float-end"
                                color="blue"
                                size="xs"
                                type="submit"
                            >
                                <div className="flex items-center gap-1">
                                    <RiCheckDoubleFill className="text-lg" />
                                    Save
                                </div>
                            </Button>
                        </div>
                    </div>
                    <div className="col-span-12 xl:col-span-4 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex">
                        <div className="w-full m-auto">
                            <TemplateBox>
                                <FilePreview object={data.files[0] ?? image} imageSize="xl" />
                                {data.msg && (
                                    <div className="break-all word-break"
                                        dangerouslySetInnerHTML={{
                                            __html: textFormatting(data.msg),
                                        }}
                                    />
                                )}
                                <div className="text-xs text-end text-slate-700">9:45</div>
                            </TemplateBox>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    );
}
