<?php

namespace App\Http\Controllers\Call;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CallController extends Controller
{
    public function index()
    {
     
        return Inertia::render('Call/Dashboard');
    }

    public function dashboard()
    {
     
        return Inertia::render('Call/Dashboard');
    }
    public function campaign()
    {
     
        return Inertia::render('Call/Campaign/Index');
    }
    public function addCampaign()
    {
     
        return Inertia::render('Call/Campaign/AddCampaign');
    }
    public function messages()
    {
     
        return Inertia::render('Call/Messages/Index');
    }
    public function bots()
    {
     
        return Inertia::render('Call/Bots/Index');
    }

    public function listKeyword()
    {
     
        return Inertia::render('Call/Bots/ListKeyword');
    }

    public function templates()
    {
        return Inertia::render('Call/Templates/Index');
    }

    public function contacts()
    {
        return Inertia::render('Call/Contacts');
    }
    public function gateways()
    {
        return Inertia::render('Call/Gateways/Index');
    }
    public function audioLibrary()
    {
        return Inertia::render('Call/AudioLibrary/Index');
    }
    public function activeCalls()
    {
        return Inertia::render('Call/ActiveCalls/Index');
    }
    public function callLogs()
    {
        return Inertia::render('Call/CallLogs/Index');
    }
}
