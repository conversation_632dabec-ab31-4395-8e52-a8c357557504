<?php

namespace App\Http\Controllers\Whatsapp;

use App\Http\Controllers\Controller;
use App\Models\WaManualSending;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required',
                'mobile' => 'required',
                'gatewayId' => 'required'
            ]);
            $manualSending = new WaManualSending(); 
            $manualSending->name = $request->name;
            $manualSending->mobile = $request->mobile;
            $manualSending->user_id = Auth::id();
            $manualSending->wa_gateway_id = $request->gatewayId;
            $manualSending->messageDateTime = now();
            $manualSending->save();
            return redirect()->back()->with(["message" => "Contact Added SuccessFully", "type" => "success"]);
        } catch (Exception $e) {
           ;
            return redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
